"""
YouTube Channel Analyser - Streamlit Frontend
A comprehensive analytics dashboard for YouTube content creators and competitor analysis.
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

# Configure Streamlit page
st.set_page_config(
    page_title="YouTube Channel Analyser",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #FF0000;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #FF0000;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)


def make_api_request(endpoint: str, method: str = "GET", data: Dict = None) -> Optional[Dict]:
    """Make API request to the backend."""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=60)
        else:
            st.error(f"Unsupported HTTP method: {method}")
            return None
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        st.error(f"API request failed: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        st.error(f"Failed to parse API response: {str(e)}")
        return None


def search_channels(query: str, max_results: int = 10) -> Optional[List[Dict]]:
    """Search for YouTube channels."""
    data = {
        "query": query,
        "max_results": max_results
    }
    
    response = make_api_request("/analysis/search-channels", "POST", data)
    if response and response.get("success"):
        return response.get("data", {}).get("channels", [])
    return None


def get_channel_basic_info(channel_id: str) -> Optional[Dict]:
    """Get basic channel information."""
    response = make_api_request(f"/analysis/channel/{channel_id}/basic-info")
    if response and response.get("success"):
        return response.get("data")
    return None


def analyze_channel(channel_id: str, max_videos: int = 50, force_refresh: bool = False) -> Optional[Dict]:
    """Perform comprehensive channel analysis."""
    data = {
        "channel_id": channel_id,
        "max_videos": max_videos,
        "force_refresh": force_refresh
    }
    
    response = make_api_request("/analysis/channel", "POST", data)
    if response and response.get("success"):
        return response.get("data")
    return None


def display_channel_overview(channel_info: Dict, metrics: Dict):
    """Display channel overview section."""
    st.subheader("📺 Channel Overview")
    
    col1, col2 = st.columns([1, 2])
    
    with col1:
        if channel_info.get('thumbnail_url'):
            st.image(channel_info['thumbnail_url'], width=200)
    
    with col2:
        st.markdown(f"### {channel_info.get('title', 'Unknown Channel')}")
        st.write(channel_info.get('description', 'No description available')[:200] + "...")
        
        # Key metrics
        col_a, col_b, col_c, col_d = st.columns(4)
        
        with col_a:
            st.metric("Subscribers", f"{channel_info.get('subscriber_count', 0):,}")
        
        with col_b:
            st.metric("Total Videos", f"{channel_info.get('video_count', 0):,}")
        
        with col_c:
            st.metric("Total Views", f"{channel_info.get('view_count', 0):,}")
        
        with col_d:
            upload_freq = metrics.get('upload_frequency', 0)
            st.metric("Upload Frequency", f"{upload_freq:.1f}/week")


def display_performance_metrics(metrics: Dict):
    """Display performance metrics section."""
    st.subheader("📈 Performance Metrics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        avg_engagement = metrics.get('avg_engagement_rate', 0)
        st.metric(
            "Avg Engagement Rate", 
            f"{avg_engagement:.2f}%",
            help="Average engagement rate across all videos"
        )
    
    with col2:
        avg_views = metrics.get('avg_views_per_video', 0)
        st.metric(
            "Avg Views per Video", 
            f"{avg_views:,.0f}",
            help="Average view count per video"
        )
    
    with col3:
        total_videos = metrics.get('total_videos', 0)
        st.metric(
            "Videos Analyzed", 
            f"{total_videos:,}",
            help="Number of videos included in analysis"
        )


def display_video_analysis(videos: List[Dict]):
    """Display video analysis section."""
    st.subheader("🎥 Video Analysis")
    
    if not videos:
        st.warning("No video data available for analysis.")
        return
    
    # Create DataFrame for analysis
    df = pd.DataFrame(videos)
    
    # Video performance chart
    fig = px.scatter(
        df, 
        x='views', 
        y='engagement_rate',
        size='likes',
        hover_data=['title', 'published_at'],
        title="Video Performance: Views vs Engagement Rate",
        labels={
            'views': 'View Count',
            'engagement_rate': 'Engagement Rate (%)',
            'likes': 'Likes'
        }
    )
    
    fig.update_layout(height=500)
    st.plotly_chart(fig, use_container_width=True)
    
    # Top performing videos
    st.subheader("🏆 Top Performing Videos")
    
    top_videos = df.nlargest(5, 'engagement_rate')[['title', 'views', 'engagement_rate', 'published_at']]
    top_videos['engagement_rate'] = top_videos['engagement_rate'].round(2)
    top_videos['views'] = top_videos['views'].apply(lambda x: f"{x:,}")
    
    st.dataframe(
        top_videos,
        column_config={
            "title": "Video Title",
            "views": "Views",
            "engagement_rate": "Engagement Rate (%)",
            "published_at": "Published Date"
        },
        hide_index=True
    )


def display_content_insights(insights: Dict):
    """Display content insights and recommendations."""
    st.subheader("💡 Content Insights")
    
    # Successful keywords
    content_patterns = insights.get('content_patterns', {})
    successful_keywords = content_patterns.get('successful_keywords', [])
    
    if successful_keywords:
        st.write("**Most Successful Keywords:**")
        keywords_df = pd.DataFrame(successful_keywords, columns=['Keyword', 'Frequency'])
        
        fig = px.bar(
            keywords_df.head(10), 
            x='Frequency', 
            y='Keyword',
            orientation='h',
            title="Top Keywords in High-Performing Videos"
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # Visual patterns
    visual_patterns = insights.get('visual_patterns', {})
    faces_correlation = visual_patterns.get('faces_correlation', 0)
    
    if faces_correlation > 0:
        st.write(f"**Face Detection Insight:** {faces_correlation:.1%} of top-performing videos include faces in thumbnails")
    
    # Recommendations
    recommendations = insights.get('recommendations', [])
    if recommendations:
        st.write("**Recommendations:**")
        for i, rec in enumerate(recommendations, 1):
            st.write(f"{i}. {rec}")


def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">📊 YouTube Channel Analyser</h1>', unsafe_allow_html=True)
    st.markdown("**Comprehensive analytics for YouTube content creators and competitor research**")
    
    # Sidebar
    st.sidebar.title("🔍 Channel Analysis")
    
    # Channel input methods
    input_method = st.sidebar.radio(
        "How would you like to find the channel?",
        ["Search by Name", "Enter Channel ID", "Enter Channel URL"]
    )
    
    channel_id = None
    
    if input_method == "Search by Name":
        search_query = st.sidebar.text_input("Search for channel:", placeholder="Enter channel name...")
        
        if search_query:
            with st.sidebar:
                with st.spinner("Searching channels..."):
                    channels = search_channels(search_query)
                
                if channels:
                    channel_options = {f"{ch['title']} ({ch['channel_id']})": ch['channel_id'] for ch in channels}
                    selected_channel = st.selectbox("Select a channel:", list(channel_options.keys()))
                    
                    if selected_channel:
                        channel_id = channel_options[selected_channel]
                else:
                    st.warning("No channels found. Try a different search term.")
    
    elif input_method == "Enter Channel ID":
        channel_id = st.sidebar.text_input(
            "Channel ID:", 
            placeholder="UC_x5XG1OV2P6uZZ5FSM9Ttw",
            help="Find the channel ID in the channel's URL"
        )
    
    elif input_method == "Enter Channel URL":
        channel_url = st.sidebar.text_input(
            "Channel URL:", 
            placeholder="https://www.youtube.com/@channelname"
        )
        
        if channel_url:
            # Extract channel ID from URL (simplified)
            if "youtube.com/channel/" in channel_url:
                channel_id = channel_url.split("youtube.com/channel/")[1].split("/")[0]
            elif "youtube.com/@" in channel_url:
                st.sidebar.warning("Please use the channel ID format instead of @username")
            else:
                st.sidebar.error("Invalid YouTube channel URL")
    
    # Analysis options
    st.sidebar.subheader("Analysis Options")
    max_videos = st.sidebar.slider("Max videos to analyze:", 10, 200, 50, 10)
    force_refresh = st.sidebar.checkbox("Force refresh (ignore cache)", help="Bypass cached data and fetch fresh results")
    
    # Analyze button
    analyze_button = st.sidebar.button("🚀 Analyze Channel", type="primary")
    
    # Main content area
    if channel_id and analyze_button:
        # Validate channel first
        with st.spinner("Validating channel..."):
            basic_info = get_channel_basic_info(channel_id)
        
        if not basic_info:
            st.error("❌ Channel not found or inaccessible. Please check the channel ID.")
            return
        
        # Display basic info
        st.success(f"✅ Found channel: **{basic_info.get('title')}**")
        
        # Perform comprehensive analysis
        with st.spinner("Performing comprehensive analysis... This may take a few minutes."):
            analysis_result = analyze_channel(channel_id, max_videos, force_refresh)
        
        if not analysis_result:
            st.error("❌ Analysis failed. Please try again.")
            return
        
        # Display results
        channel_info = analysis_result.get('channel_info', {})
        performance_metrics = analysis_result.get('performance_metrics', {})
        video_analysis = analysis_result.get('video_analysis', [])
        insights = analysis_result.get('insights', {})
        
        # Channel overview
        display_channel_overview(channel_info, performance_metrics)
        
        st.divider()
        
        # Performance metrics
        display_performance_metrics(performance_metrics)
        
        st.divider()
        
        # Video analysis
        display_video_analysis(video_analysis)
        
        st.divider()
        
        # Content insights
        display_content_insights(insights)
        
        # Download results
        st.subheader("📥 Export Results")
        
        if st.button("Download Analysis Report"):
            # Create downloadable JSON
            report_data = {
                "channel_info": channel_info,
                "performance_metrics": performance_metrics,
                "insights": insights,
                "analysis_metadata": analysis_result.get('analysis_metadata', {}),
                "generated_at": datetime.now().isoformat()
            }
            
            st.download_button(
                label="Download JSON Report",
                data=json.dumps(report_data, indent=2),
                file_name=f"youtube_analysis_{channel_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
    
    elif not channel_id:
        # Welcome message
        st.info("👈 Use the sidebar to search for a YouTube channel or enter a channel ID to begin analysis.")
        
        # Feature overview
        st.subheader("🚀 Features")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **📊 Performance Analytics**
            - Engagement rate analysis
            - View velocity tracking
            - Outlier detection
            - Growth metrics
            """)
        
        with col2:
            st.markdown("""
            **🎯 Content Analysis**
            - Keyword extraction
            - Sentiment analysis
            - Title optimization
            - Content quality scoring
            """)
        
        with col3:
            st.markdown("""
            **🎨 Visual Analysis**
            - Thumbnail analysis
            - Color pattern detection
            - Face detection
            - Visual appeal scoring
            """)


if __name__ == "__main__":
    main()
