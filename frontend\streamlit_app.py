"""
YouTube Channel Analyser - Streamlit Frontend
A comprehensive analytics dashboard for YouTube content creators and competitor analysis.
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import threading
import asyncio
import websockets
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

# Configure Streamlit page
st.set_page_config(
    page_title="YouTube Channel Analyser",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
WS_BASE_URL = "ws://localhost:8000/ws"

# Initialize session state for progress tracking
if 'progress_data' not in st.session_state:
    st.session_state.progress_data = {
        'stage': '',
        'current': 0,
        'total': 100,
        'percentage': 0,
        'estimated_time': 0,
        'is_analyzing': False,
        'analysis_complete': False,
        'analysis_success': False,
        'analysis_message': ''
    }

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #FF0000;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #FF0000;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)


def start_progress_tracking(channel_id: str):
    """Start WebSocket connection for progress tracking."""
    def run_websocket():
        async def websocket_client():
            try:
                uri = f"{WS_BASE_URL}/progress/{channel_id}"
                async with websockets.connect(uri) as websocket:
                    while True:
                        try:
                            message = await websocket.recv()
                            data = json.loads(message)

                            if data.get('type') == 'progress':
                                st.session_state.progress_data.update({
                                    'stage': data.get('stage', ''),
                                    'current': data.get('current', 0),
                                    'total': data.get('total', 100),
                                    'percentage': data.get('percentage', 0),
                                    'estimated_time': data.get('estimated_time_remaining', 0),
                                    'is_analyzing': True
                                })
                            elif data.get('type') == 'completion':
                                st.session_state.progress_data.update({
                                    'is_analyzing': False,
                                    'analysis_complete': True,
                                    'analysis_success': data.get('success', False),
                                    'analysis_message': data.get('message', '')
                                })
                                break
                        except websockets.exceptions.ConnectionClosed:
                            break
                        except Exception as e:
                            st.error(f"WebSocket error: {e}")
                            break
            except Exception as e:
                st.error(f"Failed to connect to progress tracking: {e}")

        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(websocket_client())

    # Start WebSocket in a separate thread
    thread = threading.Thread(target=run_websocket, daemon=True)
    thread.start()


def display_progress_bar():
    """Display progress bar and status."""
    progress_data = st.session_state.progress_data

    if progress_data['is_analyzing']:
        st.info("🔄 Analysis in progress...")

        # Progress bar
        progress_bar = st.progress(progress_data['percentage'] / 100)

        # Status information
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Stage", progress_data['stage'])

        with col2:
            st.metric("Progress", f"{progress_data['current']}/{progress_data['total']}")

        with col3:
            if progress_data['estimated_time'] > 0:
                minutes = int(progress_data['estimated_time'] // 60)
                seconds = int(progress_data['estimated_time'] % 60)
                time_str = f"{minutes}m {seconds}s" if minutes > 0 else f"{seconds}s"
                st.metric("Est. Time Remaining", time_str)
            else:
                st.metric("Est. Time Remaining", "Calculating...")

        # Auto-refresh every 2 seconds during analysis
        time.sleep(2)
        st.rerun()

    elif progress_data['analysis_complete']:
        if progress_data['analysis_success']:
            st.success(f"✅ {progress_data['analysis_message']}")
        else:
            st.error(f"❌ {progress_data['analysis_message']}")

        # Reset progress data after showing completion
        if st.button("Start New Analysis"):
            st.session_state.progress_data = {
                'stage': '',
                'current': 0,
                'total': 100,
                'percentage': 0,
                'estimated_time': 0,
                'is_analyzing': False,
                'analysis_complete': False,
                'analysis_success': False,
                'analysis_message': ''
            }
            st.rerun()


def make_api_request(endpoint: str, method: str = "GET", data: Dict = None) -> Optional[Dict]:
    """Make API request to the backend."""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=60)
        else:
            st.error(f"Unsupported HTTP method: {method}")
            return None
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        st.error(f"API request failed: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        st.error(f"Failed to parse API response: {str(e)}")
        return None


def search_channels(query: str, max_results: int = 10) -> Optional[List[Dict]]:
    """Search for YouTube channels."""
    data = {
        "query": query,
        "max_results": max_results
    }
    
    response = make_api_request("/analysis/search-channels", "POST", data)
    if response and response.get("success"):
        return response.get("data", {}).get("channels", [])
    return None


def get_channel_basic_info(channel_id: str) -> Optional[Dict]:
    """Get basic channel information."""
    response = make_api_request(f"/analysis/channel/{channel_id}/basic-info")
    if response and response.get("success"):
        return response.get("data")
    return None


def analyze_channel(channel_id: str, max_videos: int = 50, force_refresh: bool = False) -> Optional[Dict]:
    """Perform comprehensive channel analysis with progress tracking."""
    # Reset progress state
    st.session_state.progress_data = {
        'stage': 'Starting analysis...',
        'current': 0,
        'total': 100,
        'percentage': 0,
        'estimated_time': 0,
        'is_analyzing': True,
        'analysis_complete': False,
        'analysis_success': False,
        'analysis_message': ''
    }

    # Start progress tracking
    start_progress_tracking(channel_id)

    data = {
        "channel_id": channel_id,
        "max_videos": max_videos,
        "force_refresh": force_refresh
    }

    response = make_api_request("/analysis/channel", "POST", data)
    if response and response.get("success"):
        return response.get("data")
    return None


def display_channel_overview(channel_info: Dict, metrics: Dict):
    """Display enhanced channel overview section."""
    st.subheader("📺 Channel Overview")

    col1, col2 = st.columns([1, 2])

    with col1:
        if channel_info.get('thumbnail_url'):
            st.image(channel_info['thumbnail_url'], width=200)

        # Channel health indicators
        st.markdown("### 🏥 Channel Health")

        # Calculate health score based on metrics
        subscriber_count = channel_info.get('subscriber_count', 0)
        video_count = channel_info.get('video_count', 0)
        avg_engagement = metrics.get('avg_engagement_rate', 0)

        # Simple health scoring
        health_score = 0
        if subscriber_count > 1000: health_score += 25
        if video_count > 10: health_score += 25
        if avg_engagement > 2: health_score += 25
        if avg_engagement > 5: health_score += 25

        # Health status
        if health_score >= 75:
            st.success(f"🟢 Excellent ({health_score}/100)")
        elif health_score >= 50:
            st.info(f"🟡 Good ({health_score}/100)")
        elif health_score >= 25:
            st.warning(f"🟠 Fair ({health_score}/100)")
        else:
            st.error(f"🔴 Needs Improvement ({health_score}/100)")

    with col2:
        st.markdown(f"### {channel_info.get('title', 'Unknown Channel')}")

        # Description with expand option
        description = channel_info.get('description', 'No description available')
        if len(description) > 200:
            with st.expander("📝 Channel Description"):
                st.write(description)
        else:
            st.write(description[:200] + "..." if len(description) > 200 else description)

        # Key metrics in enhanced layout
        col_a, col_b, col_c, col_d = st.columns(4)

        with col_a:
            subscriber_count = channel_info.get('subscriber_count', 0)
            st.metric("Subscribers", f"{subscriber_count:,}")

        with col_b:
            video_count = channel_info.get('video_count', 0)
            st.metric("Total Videos", f"{video_count:,}")

        with col_c:
            total_views = channel_info.get('view_count', 0)
            st.metric("Total Views", f"{total_views:,}")

        with col_d:
            upload_freq = metrics.get('upload_frequency', 0)
            st.metric("Upload Frequency", f"{upload_freq:.1f}/week")

        # Additional calculated metrics
        st.markdown("### 📊 Calculated Metrics")
        col_e, col_f, col_g, col_h = st.columns(4)

        with col_e:
            if video_count > 0:
                avg_views_per_video = total_views / video_count
                st.metric("Avg Views/Video", f"{avg_views_per_video:,.0f}")
            else:
                st.metric("Avg Views/Video", "N/A")

        with col_f:
            if subscriber_count > 0 and total_views > 0:
                views_per_subscriber = total_views / subscriber_count
                st.metric("Views/Subscriber", f"{views_per_subscriber:.1f}")
            else:
                st.metric("Views/Subscriber", "N/A")

        with col_g:
            avg_engagement = metrics.get('avg_engagement_rate', 0)
            st.metric("Avg Engagement", f"{avg_engagement:.2f}%")

        with col_h:
            created_at = channel_info.get('created_at', '')
            if created_at:
                try:
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    years_active = (datetime.now() - created_date.replace(tzinfo=None)).days // 365
                    st.metric("Years Active", f"{years_active}")
                except:
                    st.metric("Years Active", "Unknown")
            else:
                st.metric("Years Active", "Unknown")


def display_performance_metrics(metrics: Dict):
    """Display enhanced performance metrics section."""
    st.subheader("📈 Performance Metrics")

    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        avg_engagement = metrics.get('avg_engagement_rate', 0)
        st.metric(
            "Avg Engagement Rate",
            f"{avg_engagement:.2f}%",
            help="Average engagement rate across all videos"
        )

    with col2:
        avg_views = metrics.get('avg_views_per_video', 0)
        st.metric(
            "Avg Views per Video",
            f"{avg_views:,.0f}",
            help="Average view count per video"
        )

    with col3:
        total_videos = metrics.get('total_videos', 0)
        st.metric(
            "Videos Analyzed",
            f"{total_videos:,}",
            help="Number of videos included in analysis"
        )

    with col4:
        total_views = metrics.get('total_views', 0)
        st.metric(
            "Total Views",
            f"{total_views:,.0f}",
            help="Total views across all videos"
        )

    # Performance insights
    st.markdown("### 📊 Performance Insights")

    # Create performance gauge chart
    engagement_score = min(avg_engagement * 10, 100)  # Scale to 0-100

    fig_gauge = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = engagement_score,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Channel Performance Score"},
        delta = {'reference': 50},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 25], 'color': "lightgray"},
                {'range': [25, 50], 'color': "yellow"},
                {'range': [50, 75], 'color': "orange"},
                {'range': [75, 100], 'color': "green"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))

    fig_gauge.update_layout(height=300)
    st.plotly_chart(fig_gauge, use_container_width=True)


def display_video_analysis(videos: List[Dict]):
    """Display enhanced video analysis section."""
    st.subheader("🎥 Video Analysis")

    if not videos:
        st.warning("No video data available for analysis.")
        return

    # Create DataFrame for analysis
    df = pd.DataFrame(videos)

    # Ensure required columns exist
    required_cols = ['views', 'engagement_rate', 'likes', 'title', 'published_at']
    for col in required_cols:
        if col not in df.columns:
            df[col] = 0 if col in ['views', 'engagement_rate', 'likes'] else 'Unknown'

    # Convert published_at to datetime for time series analysis
    if 'published_at' in df.columns:
        df['published_at'] = pd.to_datetime(df['published_at'], errors='coerce')
        df['days_since_published'] = (pd.Timestamp.now() - df['published_at']).dt.days

    # Create tabs for different analysis views
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Performance Overview", "📈 Trends", "🏆 Top Performers", "🔍 Detailed Analysis"])

    with tab1:
        # Performance scatter plot
        fig_scatter = px.scatter(
            df,
            x='views',
            y='engagement_rate',
            size='likes',
            color='overall_score' if 'overall_score' in df.columns else 'engagement_rate',
            hover_data=['title', 'published_at'],
            title="Video Performance: Views vs Engagement Rate",
            labels={
                'views': 'View Count',
                'engagement_rate': 'Engagement Rate (%)',
                'likes': 'Likes',
                'overall_score': 'Overall Score'
            }
        )
        fig_scatter.update_layout(height=500)
        st.plotly_chart(fig_scatter, use_container_width=True)

        # Performance distribution
        col1, col2 = st.columns(2)

        with col1:
            fig_hist_views = px.histogram(
                df,
                x='views',
                nbins=20,
                title="View Count Distribution",
                labels={'views': 'View Count', 'count': 'Number of Videos'}
            )
            st.plotly_chart(fig_hist_views, use_container_width=True)

        with col2:
            fig_hist_engagement = px.histogram(
                df,
                x='engagement_rate',
                nbins=20,
                title="Engagement Rate Distribution",
                labels={'engagement_rate': 'Engagement Rate (%)', 'count': 'Number of Videos'}
            )
            st.plotly_chart(fig_hist_engagement, use_container_width=True)

    with tab2:
        # Time series analysis
        if 'published_at' in df.columns and not df['published_at'].isna().all():
            # Views over time
            fig_timeline = px.scatter(
                df.sort_values('published_at'),
                x='published_at',
                y='views',
                size='engagement_rate',
                hover_data=['title', 'likes'],
                title="Video Performance Over Time",
                labels={
                    'published_at': 'Publication Date',
                    'views': 'View Count',
                    'engagement_rate': 'Engagement Rate (%)'
                }
            )
            fig_timeline.update_layout(height=400)
            st.plotly_chart(fig_timeline, use_container_width=True)

            # Upload frequency analysis
            if len(df) > 1:
                df_monthly = df.groupby(df['published_at'].dt.to_period('M')).size().reset_index()
                df_monthly.columns = ['Month', 'Videos_Count']
                df_monthly['Month'] = df_monthly['Month'].astype(str)

                fig_frequency = px.bar(
                    df_monthly,
                    x='Month',
                    y='Videos_Count',
                    title="Upload Frequency by Month",
                    labels={'Videos_Count': 'Number of Videos', 'Month': 'Month'}
                )
                st.plotly_chart(fig_frequency, use_container_width=True)
        else:
            st.info("Time series analysis not available - missing publication dates")

    with tab3:
        # Top performing videos
        col1, col2 = st.columns(2)

        with col1:
            st.write("**🏆 Top 10 by Engagement Rate:**")
            top_engagement = df.nlargest(10, 'engagement_rate')[['title', 'views', 'engagement_rate', 'likes']]
            top_engagement['engagement_rate'] = top_engagement['engagement_rate'].round(2)
            st.dataframe(top_engagement, use_container_width=True)

        with col2:
            st.write("**👁️ Top 10 by Views:**")
            top_views = df.nlargest(10, 'views')[['title', 'views', 'engagement_rate', 'likes']]
            top_views['engagement_rate'] = top_views['engagement_rate'].round(2)
            st.dataframe(top_views, use_container_width=True)

        # Performance comparison chart
        if len(df) >= 5:
            top_5_engagement = df.nlargest(5, 'engagement_rate')
            fig_top_comparison = px.bar(
                top_5_engagement,
                x='title',
                y=['views', 'likes'],
                title="Top 5 Videos by Engagement - Views vs Likes",
                barmode='group'
            )
            fig_top_comparison.update_xaxes(tickangle=45)
            fig_top_comparison.update_layout(height=400)
            st.plotly_chart(fig_top_comparison, use_container_width=True)

    with tab4:
        # Detailed analysis with filters
        st.write("**🔍 Detailed Video Analysis**")

        # Filters
        col1, col2, col3 = st.columns(3)

        with col1:
            min_views = st.number_input("Minimum Views", min_value=0, value=0)

        with col2:
            min_engagement = st.number_input("Minimum Engagement Rate", min_value=0.0, value=0.0, step=0.1)

        with col3:
            sort_by = st.selectbox("Sort by", ['engagement_rate', 'views', 'likes', 'published_at'])

        # Apply filters
        filtered_df = df[
            (df['views'] >= min_views) &
            (df['engagement_rate'] >= min_engagement)
        ].sort_values(sort_by, ascending=False)

        # Display filtered results
        display_columns = ['title', 'views', 'likes', 'engagement_rate', 'published_at']
        if 'overall_score' in filtered_df.columns:
            display_columns.append('overall_score')

        st.dataframe(
            filtered_df[display_columns].head(20),
            use_container_width=True,
            column_config={
                'title': st.column_config.TextColumn('Title', width='large'),
                'views': st.column_config.NumberColumn('Views', format='%d'),
                'likes': st.column_config.NumberColumn('Likes', format='%d'),
                'engagement_rate': st.column_config.NumberColumn('Engagement %', format='%.2f'),
                'overall_score': st.column_config.NumberColumn('Score', format='%.1f')
            }
        )

        st.caption(f"Showing {len(filtered_df)} videos matching criteria")


def display_content_insights(insights: Dict):
    """Display enhanced content insights and recommendations."""
    st.subheader("💡 Content Insights & Recommendations")

    # Create tabs for different insight categories
    tab1, tab2, tab3, tab4 = st.tabs(["🔤 Keywords", "🎨 Visual Patterns", "📊 Performance Patterns", "💡 Recommendations"])

    with tab1:
        # Successful keywords
        content_patterns = insights.get('content_patterns', {})
        successful_keywords = content_patterns.get('successful_keywords', [])

        if successful_keywords:
            col1, col2 = st.columns([2, 1])

            with col1:
                st.write("**🏆 Most Successful Keywords:**")
                keywords_df = pd.DataFrame(successful_keywords, columns=['Keyword', 'Frequency'])

                fig_keywords = px.bar(
                    keywords_df.head(15),
                    x='Frequency',
                    y='Keyword',
                    orientation='h',
                    title="Top Keywords in High-Performing Videos",
                    color='Frequency',
                    color_continuous_scale='viridis'
                )
                fig_keywords.update_layout(height=500)
                st.plotly_chart(fig_keywords, use_container_width=True)

            with col2:
                st.write("**📈 Keyword Performance:**")
                # Create keyword performance metrics
                total_keywords = len(successful_keywords)
                avg_frequency = sum([kw[1] for kw in successful_keywords]) / total_keywords if total_keywords > 0 else 0

                st.metric("Total Unique Keywords", total_keywords)
                st.metric("Avg Keyword Frequency", f"{avg_frequency:.1f}")

                # Top keyword insights
                if successful_keywords:
                    top_keyword = successful_keywords[0]
                    st.info(f"🎯 **Top Keyword**: '{top_keyword[0]}' appears in {top_keyword[1]} high-performing videos")

        # Sentiment analysis
        successful_sentiment = content_patterns.get('successful_sentiment', 'neutral')
        st.write("**😊 Successful Content Sentiment:**")

        sentiment_colors = {
            'positive': '🟢',
            'negative': '🔴',
            'neutral': '🟡'
        }

        sentiment_color = sentiment_colors.get(successful_sentiment, '🟡')
        st.info(f"{sentiment_color} **{successful_sentiment.title()}** sentiment performs best for this channel")

    with tab2:
        # Visual patterns
        visual_patterns = insights.get('visual_patterns', {})

        if visual_patterns:
            st.write("**🎨 Visual Analysis Insights:**")

            # Face detection insights
            face_success_rate = visual_patterns.get('face_success_rate', 0)
            avg_faces = visual_patterns.get('avg_faces_in_successful', 0)

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    "Face Success Rate",
                    f"{face_success_rate:.1f}%",
                    help="Percentage of successful videos with faces in thumbnails"
                )

            with col2:
                st.metric(
                    "Avg Faces in Top Videos",
                    f"{avg_faces:.1f}",
                    help="Average number of faces in high-performing video thumbnails"
                )

            with col3:
                visual_appeal_avg = visual_patterns.get('avg_visual_appeal', 0)
                st.metric(
                    "Avg Visual Appeal Score",
                    f"{visual_appeal_avg:.1f}/10",
                    help="Average visual appeal score of successful videos"
                )

            # Color analysis
            dominant_colors = visual_patterns.get('successful_colors', [])
            if dominant_colors:
                st.write("**🌈 Successful Color Patterns:**")
                colors_text = ", ".join([f"**{color}**" for color in dominant_colors[:5]])
                st.markdown(f"Top performing videos often use: {colors_text}")
        else:
            st.info("Visual pattern analysis not available - insufficient data")

    with tab3:
        # Performance patterns
        performance_patterns = insights.get('performance_patterns', {})

        if performance_patterns:
            st.write("**📊 Performance Pattern Analysis:**")

            # Upload timing insights
            best_upload_day = performance_patterns.get('best_upload_day', 'Unknown')
            best_upload_hour = performance_patterns.get('best_upload_hour', 'Unknown')

            col1, col2 = st.columns(2)

            with col1:
                st.info(f"📅 **Best Upload Day**: {best_upload_day}")

            with col2:
                st.info(f"🕐 **Best Upload Time**: {best_upload_hour}:00")

            # Video length insights
            optimal_duration = performance_patterns.get('optimal_duration_range', 'Unknown')
            if optimal_duration != 'Unknown':
                st.success(f"⏱️ **Optimal Video Length**: {optimal_duration}")

            # Engagement patterns
            peak_engagement_period = performance_patterns.get('peak_engagement_period', 'Unknown')
            if peak_engagement_period != 'Unknown':
                st.info(f"🚀 **Peak Engagement Period**: {peak_engagement_period}")
        else:
            st.info("Performance pattern analysis not available - insufficient data")

    with tab4:
        # Recommendations
        recommendations = insights.get('recommendations', [])

        if recommendations:
            st.write("**🎯 Actionable Recommendations:**")

            for i, rec in enumerate(recommendations, 1):
                recommendation_type = rec.get('type', 'general')
                recommendation_text = rec.get('recommendation', '')
                confidence = rec.get('confidence', 0)

                # Color code by confidence
                if confidence >= 0.8:
                    st.success(f"**{i}. {recommendation_text}** (Confidence: {confidence:.0%})")
                elif confidence >= 0.6:
                    st.info(f"**{i}. {recommendation_text}** (Confidence: {confidence:.0%})")
                else:
                    st.warning(f"**{i}. {recommendation_text}** (Confidence: {confidence:.0%})")
        else:
            # Generate basic recommendations if none exist
            st.write("**🎯 General Recommendations:**")

            basic_recommendations = [
                "🔍 **Keyword Optimization**: Use successful keywords from your top-performing videos in future titles and descriptions",
                "📅 **Consistent Upload Schedule**: Maintain regular upload frequency to build audience expectations",
                "🎨 **Thumbnail Optimization**: Analyze visual patterns from your best-performing videos for thumbnail design",
                "📊 **Engagement Focus**: Monitor and optimize for engagement rate, not just view count",
                "🎯 **Content Analysis**: Review your top-performing videos to identify successful content patterns"
            ]

            for rec in basic_recommendations:
                st.info(rec)

        # Additional insights section
        st.write("**📈 Growth Opportunities:**")

        growth_tips = [
            "🚀 **Trend Analysis**: Identify trending topics in your niche and create timely content",
            "🤝 **Collaboration**: Consider collaborating with channels of similar size for cross-promotion",
            "📱 **Platform Optimization**: Ensure your content works well across different devices and platforms",
            "💬 **Community Engagement**: Actively respond to comments to boost engagement metrics",
            "🔄 **Content Repurposing**: Transform your best-performing content into different formats"
        ]

        for tip in growth_tips:
            st.markdown(f"- {tip}")

        # Performance benchmark
        st.write("**🎯 Performance Benchmarks:**")
        st.markdown("""
        - **Excellent Engagement**: >10% engagement rate
        - **Good Engagement**: 5-10% engagement rate
        - **Average Engagement**: 2-5% engagement rate
        - **Needs Improvement**: <2% engagement rate
        """)

        st.caption("💡 These insights are based on analysis of your channel's performance patterns and industry best practices.")
    
    # Visual patterns
    visual_patterns = insights.get('visual_patterns', {})
    faces_correlation = visual_patterns.get('faces_correlation', 0)
    
    if faces_correlation > 0:
        st.write(f"**Face Detection Insight:** {faces_correlation:.1%} of top-performing videos include faces in thumbnails")
    
    # Recommendations
    recommendations = insights.get('recommendations', [])
    if recommendations:
        st.write("**Recommendations:**")
        for i, rec in enumerate(recommendations, 1):
            st.write(f"{i}. {rec}")


def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">📊 YouTube Channel Analyser</h1>', unsafe_allow_html=True)
    st.markdown("**Comprehensive analytics for YouTube content creators and competitor research**")
    
    # Sidebar
    st.sidebar.title("🔍 Channel Analysis")
    
    # Channel input methods
    input_method = st.sidebar.radio(
        "How would you like to find the channel?",
        ["Search by Name", "Enter Channel ID", "Enter Channel URL"]
    )
    
    channel_id = None
    
    if input_method == "Search by Name":
        search_query = st.sidebar.text_input("Search for channel:", placeholder="Enter channel name...")
        
        if search_query:
            with st.sidebar:
                with st.spinner("Searching channels..."):
                    channels = search_channels(search_query)
                
                if channels:
                    channel_options = {f"{ch['title']} ({ch['channel_id']})": ch['channel_id'] for ch in channels}
                    selected_channel = st.selectbox("Select a channel:", list(channel_options.keys()))
                    
                    if selected_channel:
                        channel_id = channel_options[selected_channel]
                else:
                    st.warning("No channels found. Try a different search term.")
    
    elif input_method == "Enter Channel ID":
        channel_id = st.sidebar.text_input(
            "Channel ID:", 
            placeholder="UC_x5XG1OV2P6uZZ5FSM9Ttw",
            help="Find the channel ID in the channel's URL"
        )
    
    elif input_method == "Enter Channel URL":
        channel_url = st.sidebar.text_input(
            "Channel URL:", 
            placeholder="https://www.youtube.com/@channelname"
        )
        
        if channel_url:
            # Extract channel ID from URL (simplified)
            if "youtube.com/channel/" in channel_url:
                channel_id = channel_url.split("youtube.com/channel/")[1].split("/")[0]
            elif "youtube.com/@" in channel_url:
                st.sidebar.warning("Please use the channel ID format instead of @username")
            else:
                st.sidebar.error("Invalid YouTube channel URL")
    
    # Analysis options
    st.sidebar.subheader("Analysis Options")
    max_videos = st.sidebar.slider("Max videos to analyze:", 10, 200, 50, 10)
    force_refresh = st.sidebar.checkbox("Force refresh (ignore cache)", help="Bypass cached data and fetch fresh results")
    
    # Analyze button
    analyze_button = st.sidebar.button("🚀 Analyze Channel", type="primary")
    
    # Main content area
    if channel_id and analyze_button:
        # Validate channel first
        with st.spinner("Validating channel..."):
            basic_info = get_channel_basic_info(channel_id)

        if not basic_info:
            st.error("❌ Channel not found or inaccessible. Please check the channel ID.")
            return

        # Display basic info
        st.success(f"✅ Found channel: **{basic_info.get('title')}**")

        # Perform comprehensive analysis with progress tracking
        analysis_result = analyze_channel(channel_id, max_videos, force_refresh)

        # Display progress bar during analysis
        display_progress_bar()

        if not analysis_result:
            st.error("❌ Analysis failed. Please try again.")
            return

        # Display results only if analysis is complete and successful
        if (st.session_state.progress_data['analysis_complete'] and
            st.session_state.progress_data['analysis_success']):

            # Display results
            channel_info = analysis_result.get('channel_info', {})
            performance_metrics = analysis_result.get('performance_metrics', {})
            video_analysis = analysis_result.get('video_analysis', [])
            insights = analysis_result.get('insights', {})

            # Channel overview
            display_channel_overview(channel_info, performance_metrics)
        
        st.divider()
        
        # Performance metrics
        display_performance_metrics(performance_metrics)
        
        st.divider()
        
        # Video analysis
        display_video_analysis(video_analysis)
        
        st.divider()
        
        # Content insights
        display_content_insights(insights)
        
        # Download results
        st.subheader("📥 Export Results")
        
        if st.button("Download Analysis Report"):
            # Create downloadable JSON
            report_data = {
                "channel_info": channel_info,
                "performance_metrics": performance_metrics,
                "insights": insights,
                "analysis_metadata": analysis_result.get('analysis_metadata', {}),
                "generated_at": datetime.now().isoformat()
            }
            
            st.download_button(
                label="Download JSON Report",
                data=json.dumps(report_data, indent=2),
                file_name=f"youtube_analysis_{channel_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
    
    # Display progress if analysis is in progress but no results yet
    elif st.session_state.progress_data['is_analyzing']:
        display_progress_bar()

    elif not channel_id:
        # Welcome message
        st.info("👈 Use the sidebar to search for a YouTube channel or enter a channel ID to begin analysis.")
        
        # Feature overview
        st.subheader("🚀 Features")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **📊 Performance Analytics**
            - Engagement rate analysis
            - View velocity tracking
            - Outlier detection
            - Growth metrics
            """)
        
        with col2:
            st.markdown("""
            **🎯 Content Analysis**
            - Keyword extraction
            - Sentiment analysis
            - Title optimization
            - Content quality scoring
            """)
        
        with col3:
            st.markdown("""
            **🎨 Visual Analysis**
            - Thumbnail analysis
            - Color pattern detection
            - Face detection
            - Visual appeal scoring
            """)


if __name__ == "__main__":
    main()
