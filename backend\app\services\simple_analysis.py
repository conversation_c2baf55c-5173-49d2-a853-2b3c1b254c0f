"""
Simplified analysis service without heavy dependencies.
Basic metrics calculation using built-in Python functions.
"""

import logging
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio

from .youtube_api import YouTubeAPIClient
from ..core.sqlite_database import SQLiteDatabase
from ..core.memory_cache import MemoryCache

logger = logging.getLogger(__name__)


class SimpleAnalysisService:
    """Simplified channel analysis service without pandas/numpy dependencies."""
    
    def __init__(self, database: SQLiteDatabase, cache_manager: MemoryCache):
        self.db = database
        self.cache = cache_manager
        self.youtube_client = YouTubeAPIClient(cache_manager)
    
    async def analyze_channel(self, channel_id: str, max_videos: int = 50, force_refresh: bool = False) -> Dict[str, Any]:
        """Perform simplified channel analysis."""
        try:
            logger.info(f"Starting analysis for channel: {channel_id}")
            
            # Check cache first
            if not force_refresh:
                cached_result = await self.db.get_analysis_result(channel_id)
                if cached_result:
                    logger.info("Returning cached analysis result")
                    return cached_result
            
            # Get channel info
            channel_info = await self.youtube_client.get_channel_info(channel_id)
            if not channel_info:
                raise ValueError(f"Channel not found: {channel_id}")
            
            # Get videos
            videos = await self.youtube_client.get_channel_videos(channel_id, max_videos)
            if not videos:
                raise ValueError("No videos found for channel")
            
            # Calculate metrics
            metrics = self._calculate_metrics(videos)
            
            # Basic content analysis
            content_analysis = self._analyze_content(videos)
            
            # Compile results
            result = {
                'channel_info': channel_info,
                'metrics': metrics,
                'content_analysis': content_analysis,
                'video_count': len(videos),
                'analysis_date': datetime.utcnow().isoformat(),
                'videos': videos[:10]  # Include first 10 videos as examples
            }
            
            # Store result
            await self.db.store_analysis_result(channel_id, result)
            
            logger.info(f"Analysis completed for channel: {channel_id}")
            return result
            
        except Exception as e:
            logger.error(f"Analysis failed for channel {channel_id}: {e}")
            raise
    
    def _calculate_metrics(self, videos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate basic performance metrics."""
        try:
            if not videos:
                return {}
            
            # Extract numeric values
            views = [int(v.get('view_count', 0)) for v in videos if v.get('view_count')]
            likes = [int(v.get('like_count', 0)) for v in videos if v.get('like_count')]
            comments = [int(v.get('comment_count', 0)) for v in videos if v.get('comment_count')]
            
            # Calculate engagement rates
            engagement_rates = []
            for video in videos:
                view_count = int(video.get('view_count', 0))
                like_count = int(video.get('like_count', 0))
                comment_count = int(video.get('comment_count', 0))
                
                if view_count > 0:
                    engagement_rate = ((like_count + comment_count) / view_count) * 100
                    engagement_rates.append(engagement_rate)
            
            # Basic statistics
            metrics = {
                'total_videos': len(videos),
                'total_views': sum(views) if views else 0,
                'total_likes': sum(likes) if likes else 0,
                'total_comments': sum(comments) if comments else 0,
                'avg_views': statistics.mean(views) if views else 0,
                'avg_likes': statistics.mean(likes) if likes else 0,
                'avg_comments': statistics.mean(comments) if comments else 0,
                'avg_engagement_rate': statistics.mean(engagement_rates) if engagement_rates else 0,
                'median_views': statistics.median(views) if views else 0,
                'max_views': max(views) if views else 0,
                'min_views': min(views) if views else 0
            }
            
            # Find outliers (simple method)
            if views:
                q1 = statistics.quantiles(views, n=4)[0] if len(views) >= 4 else min(views)
                q3 = statistics.quantiles(views, n=4)[2] if len(views) >= 4 else max(views)
                iqr = q3 - q1
                
                outlier_threshold_high = q3 + 1.5 * iqr
                outlier_threshold_low = q1 - 1.5 * iqr
                
                top_performers = [v for v in videos if int(v.get('view_count', 0)) > outlier_threshold_high]
                underperformers = [v for v in videos if int(v.get('view_count', 0)) < outlier_threshold_low and int(v.get('view_count', 0)) > 0]
                
                metrics['top_performers'] = top_performers[:5]  # Top 5
                metrics['underperformers'] = underperformers[:5]  # Bottom 5
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            return {}
    
    def _analyze_content(self, videos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Basic content analysis without heavy NLP dependencies."""
        try:
            if not videos:
                return {}
            
            # Analyze titles
            titles = [v.get('title', '') for v in videos if v.get('title')]
            
            # Basic title analysis
            title_lengths = [len(title) for title in titles]
            word_counts = [len(title.split()) for title in titles]
            
            # Common words (simple approach)
            all_words = []
            for title in titles:
                words = title.lower().split()
                # Filter out common stop words
                stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'}
                filtered_words = [word for word in words if word not in stop_words and len(word) > 2]
                all_words.extend(filtered_words)
            
            # Count word frequency
            word_freq = {}
            for word in all_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # Get top words
            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            
            content_analysis = {
                'avg_title_length': statistics.mean(title_lengths) if title_lengths else 0,
                'avg_word_count': statistics.mean(word_counts) if word_counts else 0,
                'top_keywords': [{'word': word, 'count': count} for word, count in top_words],
                'total_unique_words': len(word_freq)
            }
            
            return content_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing content: {e}")
            return {}
    
    async def search_channels(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Search for channels."""
        return await self.youtube_client.search_channels(query, max_results)
    
    async def get_channel_basic_info(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get basic channel information."""
        return await self.youtube_client.get_channel_info(channel_id)
    
    async def get_channel_videos(self, channel_id: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Get channel videos list."""
        return await self.youtube_client.get_channel_videos(channel_id, max_results)
