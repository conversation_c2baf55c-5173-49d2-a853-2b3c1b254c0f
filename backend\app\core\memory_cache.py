"""
Simple in-memory cache implementation.
Replaces Redis for easy testing without external dependencies.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Optional, Dict
import json

logger = logging.getLogger(__name__)

class MemoryCache:
    """Simple in-memory cache with TTL support."""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def connect(self):
        """Initialize the cache (start cleanup task)."""
        logger.info("Initializing in-memory cache")
        self._cleanup_task = asyncio.create_task(self._cleanup_expired())
    
    async def disconnect(self):
        """Cleanup cache resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        self._cache.clear()
        logger.info("Memory cache disconnected")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            if key in self._cache:
                entry = self._cache[key]
                if datetime.now(timezone.utc) < entry['expires_at']:
                    return entry['value']
                else:
                    # Expired, remove it
                    del self._cache[key]
            return None
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl_seconds: int = 3600):
        """Set value in cache with TTL."""
        try:
            now = datetime.now(timezone.utc)
            expires_at = now + timedelta(seconds=ttl_seconds)
            self._cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': now
            }
        except Exception as e:
            logger.error(f"Cache set error: {e}")
    
    async def delete(self, key: str):
        """Delete key from cache."""
        try:
            if key in self._cache:
                del self._cache[key]
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
    
    async def exists(self, key: str) -> bool:
        """Check if key exists and is not expired."""
        return await self.get(key) is not None
    
    async def clear(self):
        """Clear all cache entries."""
        self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        now = datetime.utcnow()
        total_keys = len(self._cache)
        expired_keys = sum(1 for entry in self._cache.values() if now >= entry['expires_at'])

        return {
            'total_keys': total_keys,
            'active_keys': total_keys - expired_keys,
            'expired_keys': expired_keys
        }

    async def get_youtube_api_response(self, endpoint: str, params: Dict[str, Any]) -> Optional[Any]:
        """Get cached YouTube API response."""
        import hashlib
        # Create cache key from endpoint and params
        params_str = json.dumps(params, sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        cache_key = f"youtube:api:{endpoint}:{params_hash}"
        return await self.get(cache_key)

    async def cache_youtube_api_response(self, endpoint: str, params: Dict[str, Any], response: Any, ttl_seconds: int = 3600):
        """Cache YouTube API response."""
        import hashlib
        # Create cache key from endpoint and params
        params_str = json.dumps(params, sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        cache_key = f"youtube:api:{endpoint}:{params_hash}"
        await self.set(cache_key, response, ttl_seconds)
    
    async def _cleanup_expired(self):
        """Background task to cleanup expired entries."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                now = datetime.utcnow()
                expired_keys = [
                    key for key, entry in self._cache.items() 
                    if now >= entry['expires_at']
                ]
                
                for key in expired_keys:
                    del self._cache[key]
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")


# Global cache instance
_cache = MemoryCache()


async def init_memory_cache():
    """Initialize memory cache."""
    await _cache.connect()


async def close_memory_cache():
    """Close memory cache."""
    await _cache.disconnect()


async def get_memory_cache() -> MemoryCache:
    """Get memory cache instance."""
    return _cache
