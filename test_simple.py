#!/usr/bin/env python3
"""
Simple test script to verify the YouTube Channel Analyser works.
Tests basic functionality without external dependencies.
"""

import asyncio
import sys
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

async def test_basic_functionality():
    """Test basic app functionality."""
    print("🧪 Testing YouTube Channel Analyser...")
    
    try:
        # Test database connection
        print("📁 Testing SQLite database...")
        from backend.app.core.sqlite_database import init_sqlite_database, get_sqlite_database, close_sqlite_database
        
        await init_sqlite_database()
        db = await get_sqlite_database()
        print("✅ SQLite database connected successfully")
        
        # Test cache
        print("💾 Testing memory cache...")
        from backend.app.core.memory_cache import init_memory_cache, get_memory_cache, close_memory_cache
        
        await init_memory_cache()
        cache = await get_memory_cache()
        
        # Test cache operations
        await cache.set("test_key", {"test": "value"}, 60)
        result = await cache.get("test_key")
        assert result == {"test": "value"}, "Cache test failed"
        print("✅ Memory cache working correctly")
        
        # Test YouTube API client (without making actual API calls)
        print("🎬 Testing YouTube API client...")
        from backend.app.services.youtube_api import YouTubeAPIClient
        
        youtube_client = YouTubeAPIClient(cache)
        print("✅ YouTube API client initialized")
        
        # Test analysis service initialization
        print("📊 Testing analysis service...")
        from backend.app.services.analysis_service import ChannelAnalysisService
        
        analysis_service = ChannelAnalysisService(db, cache)
        print("✅ Analysis service initialized")
        
        # Cleanup
        await close_memory_cache()
        await close_sqlite_database()
        
        print("\n🎉 All tests passed! The application is ready to run.")
        print("\n📋 Next steps:")
        print("1. Make sure your YouTube API key is in the .env file")
        print("2. Run: python start_app.py")
        print("3. Open: http://localhost:8501")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    sys.exit(0 if success else 1)
