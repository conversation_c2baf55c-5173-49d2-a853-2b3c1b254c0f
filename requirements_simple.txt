# Simplified requirements for Python 3.13 compatibility
# Core backend dependencies only

# Backend Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
python-multipart>=0.0.6

# Database
aiosqlite>=0.19.0

# YouTube API
google-auth>=2.25.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.2.0
google-api-python-client>=2.110.0

# Basic data processing (avoid pandas compilation issues)
# pandas>=2.2.0  # Comment out if installation fails
# numpy>=1.26.0  # Comment out if installation fails

# NLP (lightweight)
textblob>=0.17.0
yake>=0.4.8

# Computer Vision (essential only)
Pillow>=10.2.0
# opencv-python>=4.9.0  # Comment out if installation fails
# scikit-learn>=1.4.0   # Comment out if installation fails

# Utilities
python-dotenv>=1.0.0
requests>=2.31.0

# Development
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.25.0
