"""
SQLite database connection and management.
Simple file-based database for easy testing without external dependencies.
"""

import logging
import aiosqlite
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class SQLiteDatabase:
    """SQLite database manager for simple file-based storage."""
    
    def __init__(self):
        self.db_path = Path("data/youtube_analyser.db")
        self.connection: Optional[aiosqlite.Connection] = None
    
    async def connect(self):
        """Connect to SQLite database."""
        try:
            # Create data directory if it doesn't exist
            self.db_path.parent.mkdir(exist_ok=True)
            
            self.connection = await aiosqlite.connect(self.db_path)
            
            # Enable foreign keys and WAL mode for better performance
            await self.connection.execute("PRAGMA foreign_keys = ON")
            await self.connection.execute("PRAGMA journal_mode = WAL")
            
            logger.info(f"Connected to SQLite database: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to connect to SQLite: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from SQLite database."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from SQLite database")
    
    async def create_tables(self):
        """Create database tables."""
        try:
            # Analysis results table
            await self.connection.execute("""
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id TEXT UNIQUE NOT NULL,
                    analysis_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Cache table
            await self.connection.execute("""
                CREATE TABLE IF NOT EXISTS cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cache_key TEXT UNIQUE NOT NULL,
                    cache_value TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            await self.connection.execute("CREATE INDEX IF NOT EXISTS idx_channel_id ON analysis_results(channel_id)")
            await self.connection.execute("CREATE INDEX IF NOT EXISTS idx_cache_key ON cache(cache_key)")
            await self.connection.execute("CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache(expires_at)")
            
            await self.connection.commit()
            logger.info("Database tables created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise
    
    async def store_analysis_result(self, channel_id: str, analysis_data: Dict[str, Any]):
        """Store analysis result in database."""
        try:
            data_json = json.dumps(analysis_data)
            now = datetime.utcnow().isoformat()
            
            await self.connection.execute("""
                INSERT OR REPLACE INTO analysis_results 
                (channel_id, analysis_data, created_at, updated_at)
                VALUES (?, ?, COALESCE((SELECT created_at FROM analysis_results WHERE channel_id = ?), ?), ?)
            """, (channel_id, data_json, channel_id, now, now))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"Failed to store analysis result: {e}")
            raise
    
    async def get_analysis_result(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get analysis result from database."""
        try:
            cursor = await self.connection.execute("""
                SELECT analysis_data FROM analysis_results WHERE channel_id = ?
            """, (channel_id,))
            
            row = await cursor.fetchone()
            if row:
                return json.loads(row[0])
            return None
            
        except Exception as e:
            logger.error(f"Failed to get analysis result: {e}")
            return None


# Global database instance
_database = SQLiteDatabase()


async def init_sqlite_database():
    """Initialize SQLite database connection."""
    await _database.connect()
    await _database.create_tables()


async def close_sqlite_database():
    """Close SQLite database connection."""
    await _database.disconnect()


async def get_sqlite_database() -> SQLiteDatabase:
    """Get SQLite database instance."""
    return _database
