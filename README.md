# YouTube Channel Analyser

A comprehensive analytics platform for YouTube content creators and competitor analysis. Analyze channel performance, identify successful content patterns, and optimize your content strategy using public YouTube data.

## 🚀 Features

### 📊 Performance Analytics
- **Engagement Rate Analysis**: Calculate and track engagement across videos
- **View Velocity Tracking**: Monitor how quickly videos gain views
- **Outlier Detection**: Identify top-performing and underperforming content
- **Upload Frequency Analysis**: Track posting consistency

### 🎯 Content Analysis
- **Keyword Extraction**: Identify successful keywords using YAKE algorithm
- **Sentiment Analysis**: Analyze title and description sentiment
- **Content Quality Scoring**: Comprehensive content assessment
- **Title Optimization**: Get recommendations for better titles

### 🎨 Visual Analysis
- **Thumbnail Analysis**: Computer vision analysis of thumbnail effectiveness
- **Color Pattern Detection**: Identify successful color schemes
- **Face Detection**: Analyze impact of faces in thumbnails
- **Visual Appeal Scoring**: Quantify thumbnail attractiveness

### 🔍 Competitor Research
- **Public Data Only**: Analyze any YouTube channel without authentication
- **Channel Comparison**: Compare performance against competitors
- **Trend Analysis**: Identify successful content patterns
- **Market Insights**: Understand what works in your niche

## 🏗️ Architecture

- **Backend**: FastAPI (Python) - High-performance async API
- **Frontend**: Streamlit - Interactive dashboard
- **Database**: MongoDB - Flexible document storage
- **Cache**: Redis - Fast data caching and quota management
- **APIs**: YouTube Data API v3 (public data only)

## ⚡ Quick Start

### Prerequisites
- Python 3.8+
- YouTube Data API v3 key ([Get one here](https://developers.google.com/youtube/v3/getting-started))
- MongoDB (local or [MongoDB Atlas](https://www.mongodb.com/cloud/atlas))
- Redis (local or [Redis Cloud](https://redis.com/))

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd youtube-channel-analyser
   pip install -r requirements.txt
   pip install -r frontend/requirements.txt
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings (see Configuration section)
   ```

3. **Start the application**
   ```bash
   python start_app.py
   ```

4. **Access the application**
   - 📊 **Dashboard**: http://localhost:8501
   - 🔧 **API**: http://localhost:8000
   - 📚 **API Docs**: http://localhost:8000/docs

## ⚙️ Configuration

### Required Settings (.env file)

```env
# YouTube API (Required)
YOUTUBE_API_KEY=your_youtube_api_key_here

# Database (Required)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=youtube_analyser

# Cache (Required)
REDIS_URL=redis://localhost:6379/0
```

### Optional Settings

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
ENVIRONMENT=development

# Analysis Configuration
OUTLIER_METHOD=iqr
OUTLIER_THRESHOLD=1.5
MAX_VIDEOS_PER_BATCH=50

# Cache Configuration
CACHE_TTL_HOURS=24
DAILY_QUOTA_LIMIT=10000
```

## 📖 Usage

### Using the Dashboard

1. **Search for Channels**: Use the sidebar to search by channel name
2. **Enter Channel ID**: Directly input a YouTube channel ID
3. **Configure Analysis**: Set max videos to analyze (10-200)
4. **Run Analysis**: Click "Analyze Channel" and wait for results
5. **Explore Results**: View performance metrics, content insights, and recommendations
6. **Export Data**: Download analysis results as JSON

### Using the API

```python
import requests

# Search for channels
response = requests.post("http://localhost:8000/api/v1/analysis/search-channels", json={
    "query": "tech review",
    "max_results": 10
})

# Analyze a channel
response = requests.post("http://localhost:8000/api/v1/analysis/channel", json={
    "channel_id": "UC_x5XG1OV2P6uZZ5FSM9Ttw",
    "max_videos": 50,
    "force_refresh": False
})

analysis = response.json()
```

## 🛠️ Development

### Project Structure
```
youtube-channel-analyser/
├── backend/
│   ├── app/
│   │   ├── api/           # FastAPI endpoints
│   │   ├── core/          # Configuration & database
│   │   ├── services/      # Business logic
│   │   └── models/        # Data models
│   └── main.py           # FastAPI application
├── frontend/
│   └── streamlit_app.py  # Streamlit dashboard
├── requirements.txt      # Backend dependencies
├── start_app.py         # Application launcher
└── .env.example         # Environment template
```

### Running Services Separately

**Backend only:**
```bash
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend only:**
```bash
cd frontend
streamlit run streamlit_app.py --server.port 8501
```

## 🔧 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/analysis/channel` | POST | Comprehensive channel analysis |
| `/api/v1/analysis/channel/{id}` | GET | Get existing analysis |
| `/api/v1/analysis/search-channels` | POST | Search for channels |
| `/api/v1/analysis/channel/{id}/basic-info` | GET | Basic channel info |
| `/api/v1/analysis/channel/{id}/videos` | GET | Channel videos list |
| `/health` | GET | Health check |

## 📊 Analysis Components

### Metrics Calculation
- Engagement rate: `(likes + comments) / views * 100`
- View velocity: `views / days_since_publish`
- Upload frequency: `total_videos / channel_age_weeks`

### Outlier Detection
- **IQR Method**: Identifies videos outside 1.5 * IQR
- **Z-Score Method**: Identifies videos with |z-score| > 3
- Configurable thresholds and methods

### NLP Analysis
- **YAKE**: Keyword extraction algorithm
- **TextBlob**: Sentiment analysis
- **Linguistic Features**: Word count, readability metrics

### Computer Vision
- **OpenCV**: Face detection in thumbnails
- **Color Analysis**: Dominant colors and temperature
- **Composition**: Edge density, contrast, symmetry

## 🚀 Deployment

### Local Development
Use the provided `start_app.py` script for easy local development.

### Production Deployment
- **Backend**: Deploy FastAPI to Heroku, AWS, or Google Cloud
- **Database**: Use MongoDB Atlas for managed MongoDB
- **Cache**: Use Redis Cloud for managed Redis
- **Frontend**: Deploy Streamlit to Streamlit Cloud

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- 📖 Check the [API documentation](http://localhost:8000/docs) when running
- 🐛 [Create an issue](https://github.com/your-repo/issues) for bugs
- 💡 [Request features](https://github.com/your-repo/issues) for enhancements

## 🙏 Acknowledgments

- YouTube Data API v3 for public channel data
- YAKE algorithm for keyword extraction
- OpenCV for computer vision analysis
- FastAPI and Streamlit for the application framework
