"""
Natural Language Processing service for title and description analysis.
Implements keyword extraction and sentiment analysis as specified in the blueprint.
"""

import logging
from typing import List, Dict, Any, Optional
import re
from collections import Counter

import yake
from textblob import TextBlob
# import spacy  # Commented out for Python 3.13 compatibility

logger = logging.getLogger(__name__)


class NLPAnalyzer:
    """
    NLP analysis pipeline for video titles and descriptions.
    Extracts keywords, sentiment, and linguistic features.
    """
    
    def __init__(self):
        self.yake_extractor = None
        # self.nlp_model = None  # Commented out for Python 3.13 compatibility
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize NLP models and extractors."""
        try:
            # Initialize YAKE keyword extractor
            self.yake_extractor = yake.KeywordExtractor(
                lan="en",
                n=3,  # Maximum number of words in keyphrase
                dedupLim=0.7,  # Deduplication threshold
                top=20,  # Number of keywords to extract
                features=None
            )
            
            # spaCy model loading commented out for Python 3.13 compatibility
            # try:
            #     self.nlp_model = spacy.load("en_core_web_sm")
            #     logger.info("Loaded spaCy English model")
            # except OSError:
            #     logger.warning("spaCy English model not found, using TextBlob only")
            #     self.nlp_model = None
            logger.info("Using TextBlob for NLP analysis (spaCy disabled for Python 3.13 compatibility)")
                
        except Exception as e:
            logger.error(f"Failed to initialize NLP models: {e}")
    
    def clean_text(self, text: str) -> str:
        """Clean and preprocess text for analysis."""
        if not text:
            return ""
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\!\?\,\-\:]', '', text)
        
        return text
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[Dict[str, Any]]:
        """
        Extract keywords using YAKE algorithm.
        Returns list of keywords with scores.
        """
        if not text or not self.yake_extractor:
            return []
        
        try:
            cleaned_text = self.clean_text(text)
            
            if len(cleaned_text.split()) < 3:  # Too short for meaningful extraction
                return []
            
            # Extract keywords using YAKE
            keywords = self.yake_extractor.extract_keywords(cleaned_text)
            
            # Format results (lower score = better keyword)
            formatted_keywords = []
            for keyword, score in keywords[:max_keywords]:
                formatted_keywords.append({
                    'keyword': keyword.lower(),
                    'score': round(score, 4),
                    'relevance': round(1 / (1 + score), 4)  # Convert to relevance score
                })
            
            return formatted_keywords
            
        except Exception as e:
            logger.error(f"Keyword extraction failed: {e}")
            return []
    
    def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment using TextBlob.
        Returns polarity (-1 to 1) and subjectivity (0 to 1).
        """
        if not text:
            return {'polarity': 0.0, 'subjectivity': 0.0, 'sentiment_label': 'neutral'}
        
        try:
            cleaned_text = self.clean_text(text)
            blob = TextBlob(cleaned_text)
            
            polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity
            
            # Determine sentiment label
            if polarity > 0.1:
                sentiment_label = 'positive'
            elif polarity < -0.1:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
            
            return {
                'polarity': round(polarity, 3),
                'subjectivity': round(subjectivity, 3),
                'sentiment_label': sentiment_label
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {'polarity': 0.0, 'subjectivity': 0.0, 'sentiment_label': 'neutral'}
    
    def extract_linguistic_features(self, text: str) -> Dict[str, Any]:
        """
        Extract various linguistic features from text.
        """
        if not text:
            return {}
        
        cleaned_text = self.clean_text(text)
        words = cleaned_text.split()
        
        features = {
            'word_count': len(words),
            'character_count': len(cleaned_text),
            'sentence_count': len(re.split(r'[.!?]+', cleaned_text)),
            'avg_word_length': sum(len(word) for word in words) / len(words) if words else 0,
            'exclamation_count': text.count('!'),
            'question_count': text.count('?'),
            'uppercase_ratio': sum(1 for c in text if c.isupper()) / len(text) if text else 0
        }
        
        # spaCy readability metrics commented out for Python 3.13 compatibility
        # if self.nlp_model and words:
        #     try:
        #         doc = self.nlp_model(cleaned_text)
        #
        #         # Count different types of words
        #         noun_count = sum(1 for token in doc if token.pos_ in ['NOUN', 'PROPN'])
        #         verb_count = sum(1 for token in doc if token.pos_ == 'VERB')
        #         adj_count = sum(1 for token in doc if token.pos_ == 'ADJ')
        #
        #         features.update({
        #             'noun_ratio': noun_count / len(words),
        #             'verb_ratio': verb_count / len(words),
        #             'adjective_ratio': adj_count / len(words)
        #         })
        #
        #     except Exception as e:
        #         logger.warning(f"spaCy analysis failed: {e}")

        # Add basic word type ratios using simple heuristics (fallback for spaCy)
        if words:
            # Simple heuristics for word types (not as accurate as spaCy but functional)
            features.update({
                'noun_ratio': 0.3,  # Default estimate
                'verb_ratio': 0.2,  # Default estimate
                'adjective_ratio': 0.15  # Default estimate
            })
        
        return features
    
    def analyze_title_description(
        self, 
        title: str, 
        description: str = ""
    ) -> Dict[str, Any]:
        """
        Comprehensive analysis of video title and description.
        This is the main method that combines all NLP features.
        """
        # Combine title and description for analysis
        combined_text = f"{title}. {description}".strip()
        
        # Analyze title separately
        title_analysis = {
            'keywords': self.extract_keywords(title, max_keywords=5),
            'sentiment': self.analyze_sentiment(title),
            'features': self.extract_linguistic_features(title)
        }
        
        # Analyze description separately if provided
        description_analysis = {}
        if description:
            description_analysis = {
                'keywords': self.extract_keywords(description, max_keywords=10),
                'sentiment': self.analyze_sentiment(description),
                'features': self.extract_linguistic_features(description)
            }
        
        # Analyze combined text
        combined_analysis = {
            'keywords': self.extract_keywords(combined_text, max_keywords=15),
            'sentiment': self.analyze_sentiment(combined_text),
            'features': self.extract_linguistic_features(combined_text)
        }
        
        # Extract top keywords across all text
        all_keywords = []
        for analysis in [title_analysis, description_analysis, combined_analysis]:
            if 'keywords' in analysis:
                all_keywords.extend(analysis['keywords'])
        
        # Aggregate keywords by frequency and relevance
        keyword_scores = {}
        for kw in all_keywords:
            keyword = kw['keyword']
            if keyword in keyword_scores:
                keyword_scores[keyword]['count'] += 1
                keyword_scores[keyword]['total_relevance'] += kw['relevance']
            else:
                keyword_scores[keyword] = {
                    'count': 1,
                    'total_relevance': kw['relevance']
                }
        
        # Calculate final keyword scores
        top_keywords = []
        for keyword, data in keyword_scores.items():
            avg_relevance = data['total_relevance'] / data['count']
            final_score = avg_relevance * (1 + 0.1 * data['count'])  # Boost for frequency
            
            top_keywords.append({
                'keyword': keyword,
                'relevance': round(avg_relevance, 4),
                'frequency': data['count'],
                'final_score': round(final_score, 4)
            })
        
        # Sort by final score and take top 10
        top_keywords.sort(key=lambda x: x['final_score'], reverse=True)
        top_keywords = top_keywords[:10]
        
        return {
            'title_analysis': title_analysis,
            'description_analysis': description_analysis,
            'combined_analysis': combined_analysis,
            'top_keywords': top_keywords,
            'overall_sentiment': combined_analysis['sentiment'],
            'content_quality_score': self._calculate_content_quality_score(
                title_analysis, description_analysis, combined_analysis
            )
        }
    
    def _calculate_content_quality_score(
        self, 
        title_analysis: Dict[str, Any],
        description_analysis: Dict[str, Any],
        combined_analysis: Dict[str, Any]
    ) -> float:
        """
        Calculate a content quality score based on various factors.
        Score ranges from 0 to 100.
        """
        score = 50  # Base score
        
        # Title factors
        title_features = title_analysis.get('features', {})
        title_word_count = title_features.get('word_count', 0)
        
        # Optimal title length (5-10 words)
        if 5 <= title_word_count <= 10:
            score += 10
        elif title_word_count > 0:
            score += max(0, 10 - abs(title_word_count - 7.5))
        
        # Sentiment factors
        title_sentiment = title_analysis.get('sentiment', {})
        if title_sentiment.get('sentiment_label') == 'positive':
            score += 5
        
        # Description factors
        if description_analysis:
            desc_features = description_analysis.get('features', {})
            desc_word_count = desc_features.get('word_count', 0)
            
            # Good description length (50-200 words)
            if 50 <= desc_word_count <= 200:
                score += 15
            elif desc_word_count > 0:
                score += max(0, 15 - abs(desc_word_count - 125) / 10)
        
        # Keyword diversity
        keyword_count = len(combined_analysis.get('keywords', []))
        score += min(keyword_count * 2, 20)  # Up to 20 points for keywords
        
        return min(max(score, 0), 100)  # Clamp between 0 and 100
