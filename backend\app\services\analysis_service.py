"""
Main analysis orchestrator service.
Coordinates all analysis components for comprehensive channel insights.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone
import asyncio
import time

from .youtube_client import YouTubeAPIClient
from .metrics_service import <PERSON><PERSON><PERSON>yzer
from .nlp_service import NL<PERSON><PERSON>y<PERSON>
from .cv_service import Thumb<PERSON><PERSON><PERSON><PERSON><PERSON>
from ..core.sqlite_database import SQLiteDatabase
from ..core.memory_cache import MemoryCache

logger = logging.getLogger(__name__)


class ChannelAnalysisService:
    """
    Main service that orchestrates comprehensive channel analysis.
    Combines YouTube data, metrics, NLP, and computer vision analysis.
    """

    def __init__(self, database: SQLiteDatabase, cache_manager: MemoryCache):
        self.db = database
        self.cache = cache_manager
        self.youtube_client = YouTubeAPIClient(cache_manager)
        self.video_analyzer = VideoAnalyzer()
        self.nlp_analyzer = NLPAnalyzer()
        self.cv_analyzer = ThumbnailAnalyzer()
        self.progress_callback: Optional[Callable[[str, int, int, float], None]] = None

    def set_progress_callback(self, callback):
        """Set callback function for progress updates."""
        self.progress_callback = callback

    async def _update_progress(self, stage: str, current: int, total: int, estimated_time: float = 0):
        """Update progress if callback is set."""
        if self.progress_callback:
            try:
                await self.progress_callback(stage, current, total, estimated_time)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")

    async def analyze_channel(
        self,
        channel_id: str,
        max_videos: int = 50,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of a YouTube channel.
        This is the main entry point for channel analysis.
        """
        try:
            start_time = time.time()
            logger.info(f"Starting analysis for channel: {channel_id}")
            await self._update_progress("Initializing analysis", 0, 100, 0)

            # Check cache first (unless force refresh)
            if not force_refresh:
                cached_result = await self._get_cached_analysis(channel_id)
                if cached_result:
                    logger.info(f"Returning cached analysis for channel: {channel_id}")
                    await self._update_progress("Analysis complete (cached)", 100, 100, 0)
                    return cached_result

            # Step 1: Get channel information
            await self._update_progress("Fetching channel information", 10, 100, 5)
            channel_info = await self.youtube_client.get_channel_info(channel_id)
            if not channel_info or 'items' not in channel_info or not channel_info['items']:
                return {'error': 'Channel not found or inaccessible'}

            channel_data = channel_info['items'][0]

            # Step 2: Get channel videos
            await self._update_progress("Fetching video list", 20, 100, 10)
            videos_list = await self.youtube_client.get_channel_videos(channel_id, max_videos)
            if not videos_list:
                return {'error': 'No videos found for this channel'}

            # Extract video IDs for batch processing
            video_ids = [item['snippet']['resourceId']['videoId'] for item in videos_list]
            total_videos = len(video_ids)

            # Step 3: Get detailed video information in batches
            await self._update_progress("Fetching video details", 30, 100, 20)
            all_videos_data = []
            batch_size = 50  # YouTube API limit

            for i, batch_start in enumerate(range(0, len(video_ids), batch_size)):
                batch_ids = video_ids[batch_start:batch_start + batch_size]
                batch_data = await self.youtube_client.get_videos_batch(batch_ids)

                if batch_data and 'items' in batch_data:
                    all_videos_data.extend(batch_data['items'])

                # Update progress for video fetching
                progress = 30 + (i + 1) * 20 // ((len(video_ids) + batch_size - 1) // batch_size)
                elapsed = time.time() - start_time
                estimated_total = elapsed * 100 / progress if progress > 0 else 0
                remaining = max(0, estimated_total - elapsed)
                await self._update_progress(f"Fetching video details ({len(all_videos_data)}/{total_videos})",
                                    progress, 100, remaining)

            # Step 4: Perform comprehensive analysis
            await self._update_progress("Analyzing videos", 50, 100, 60)
            analysis_result = await self._perform_comprehensive_analysis(
                channel_data, all_videos_data, start_time
            )

            # Step 5: Cache the result
            await self._update_progress("Caching results", 90, 100, 5)
            await self._cache_analysis_result(channel_id, analysis_result)

            # Step 6: Store in database
            await self._update_progress("Saving to database", 95, 100, 2)
            await self._store_analysis_result(channel_id, analysis_result)

            await self._update_progress("Analysis complete", 100, 100, 0)
            logger.info(f"Analysis completed for channel: {channel_id}")
            return analysis_result

        except Exception as e:
            logger.error(f"Analysis failed for channel {channel_id}: {e}")
            return {'error': f'Analysis failed: {str(e)}'}
    
    async def _perform_comprehensive_analysis(
        self,
        channel_data: Dict[str, Any],
        videos_data: List[Dict[str, Any]],
        start_time: float = None
    ) -> Dict[str, Any]:
        """Perform all analysis components on the channel and videos."""
        if start_time is None:
            start_time = time.time()

        total_videos = len(videos_data)

        # Basic metrics analysis
        await self._update_progress("Calculating performance metrics", 52, 100, 30)
        performance_analysis = self.video_analyzer.analyze_channel_performance(
            videos_data, channel_data
        )

        # Prepare for concurrent analysis
        nlp_tasks = []
        cv_tasks = []

        # Analyze each video's content
        await self._update_progress("Preparing content analysis", 55, 100, 25)
        for video in videos_data:
            snippet = video.get('snippet', {})
            title = snippet.get('title', '')
            description = snippet.get('description', '')

            # NLP analysis task
            nlp_task = self._analyze_video_nlp(title, description)
            nlp_tasks.append(nlp_task)

            # Computer vision analysis task
            thumbnails = snippet.get('thumbnails', {})
            thumbnail_url = (
                thumbnails.get('maxres', {}).get('url') or
                thumbnails.get('high', {}).get('url') or
                thumbnails.get('medium', {}).get('url', '')
            )

            if thumbnail_url:
                cv_task = self._analyze_video_thumbnail(thumbnail_url)
                cv_tasks.append(cv_task)
            else:
                cv_tasks.append(asyncio.create_task(self._empty_cv_result()))

        # Run NLP and CV analysis concurrently
        await self._update_progress(f"Analyzing content ({total_videos} videos)", 60, 100, 20)
        nlp_results = await asyncio.gather(*nlp_tasks, return_exceptions=True)

        await self._update_progress(f"Processing thumbnails ({total_videos} images)", 75, 100, 10)
        cv_results = await asyncio.gather(*cv_tasks, return_exceptions=True)
        
        # Combine all analysis results
        enhanced_videos = []
        for i, video in enumerate(videos_data):
            video_analysis = performance_analysis['videos'][i] if i < len(performance_analysis['videos']) else {}
            
            # Add NLP results
            nlp_result = nlp_results[i] if i < len(nlp_results) and not isinstance(nlp_results[i], Exception) else {}
            
            # Add CV results
            cv_result = cv_results[i] if i < len(cv_results) and not isinstance(cv_results[i], Exception) else {}
            
            enhanced_video = {
                **video_analysis,
                'nlp_analysis': nlp_result,
                'visual_analysis': cv_result,
                'overall_score': self._calculate_overall_video_score(video_analysis, nlp_result, cv_result)
            }
            
            enhanced_videos.append(enhanced_video)
        
        # Generate insights and recommendations
        insights = self._generate_insights(enhanced_videos, performance_analysis)
        
        return {
            'channel_id': channel_data.get('id'),
            'channel_info': {
                'title': channel_data.get('snippet', {}).get('title', ''),
                'description': channel_data.get('snippet', {}).get('description', ''),
                'subscriber_count': int(channel_data.get('statistics', {}).get('subscriberCount', 0)),
                'video_count': int(channel_data.get('statistics', {}).get('videoCount', 0)),
                'view_count': int(channel_data.get('statistics', {}).get('viewCount', 0)),
                'created_at': channel_data.get('snippet', {}).get('publishedAt', ''),
                'thumbnail_url': channel_data.get('snippet', {}).get('thumbnails', {}).get('high', {}).get('url', '')
            },
            'performance_metrics': performance_analysis['channel_metrics'],
            'video_analysis': enhanced_videos,
            'outliers': performance_analysis['outliers'],
            'insights': insights,
            'analysis_metadata': {
                'analyzed_at': datetime.now(timezone.utc).isoformat(),
                'total_videos_analyzed': len(enhanced_videos),
                'analysis_version': '1.0'
            }
        }
    
    async def _analyze_video_nlp(self, title: str, description: str) -> Dict[str, Any]:
        """Analyze video NLP in async context."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.nlp_analyzer.analyze_title_description, 
            title, 
            description
        )
    
    async def _analyze_video_thumbnail(self, thumbnail_url: str) -> Dict[str, Any]:
        """Analyze video thumbnail in async context."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.cv_analyzer.analyze_thumbnail, 
            thumbnail_url
        )
    
    async def _empty_cv_result(self) -> Dict[str, Any]:
        """Return empty CV result for videos without thumbnails."""
        return {'analysis_success': False, 'error': 'No thumbnail available'}
    
    def _calculate_overall_video_score(
        self, 
        performance: Dict[str, Any], 
        nlp: Dict[str, Any], 
        cv: Dict[str, Any]
    ) -> float:
        """Calculate overall video quality score combining all factors."""
        score = 0
        weight_sum = 0
        
        # Performance metrics (40% weight)
        if 'engagement_rate' in performance:
            score += performance['engagement_rate'] * 0.4
            weight_sum += 0.4
        
        # Content quality (30% weight)
        if 'content_quality_score' in nlp:
            score += nlp['content_quality_score'] * 0.3
            weight_sum += 0.3
        
        # Visual appeal (30% weight)
        if 'visual_appeal_score' in cv:
            score += cv['visual_appeal_score'] * 0.3
            weight_sum += 0.3
        
        return score / weight_sum if weight_sum > 0 else 0
    
    def _generate_insights(
        self, 
        videos: List[Dict[str, Any]], 
        performance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate actionable insights from the analysis."""
        
        insights = {
            'content_patterns': {},
            'visual_patterns': {},
            'performance_patterns': {},
            'recommendations': []
        }
        
        if not videos:
            return insights
        
        # Analyze top performing videos
        top_videos = sorted(videos, key=lambda x: x.get('engagement_rate', 0), reverse=True)[:5]
        
        # Content patterns from top videos
        top_keywords = {}
        top_sentiments = []
        
        for video in top_videos:
            nlp_data = video.get('nlp_analysis', {})
            if 'top_keywords' in nlp_data:
                for kw in nlp_data['top_keywords']:
                    keyword = kw['keyword']
                    top_keywords[keyword] = top_keywords.get(keyword, 0) + 1
            
            if 'overall_sentiment' in nlp_data:
                top_sentiments.append(nlp_data['overall_sentiment']['sentiment_label'])
        
        insights['content_patterns'] = {
            'successful_keywords': sorted(top_keywords.items(), key=lambda x: x[1], reverse=True)[:10],
            'successful_sentiment': max(set(top_sentiments), key=top_sentiments.count) if top_sentiments else 'neutral'
        }
        
        # Visual patterns from top videos
        has_faces_count = sum(1 for v in top_videos if v.get('visual_analysis', {}).get('faces', {}).get('has_faces', False))
        
        insights['visual_patterns'] = {
            'faces_in_top_videos': f"{has_faces_count}/{len(top_videos)}",
            'faces_correlation': has_faces_count / len(top_videos) if top_videos else 0
        }
        
        # Generate recommendations
        recommendations = []
        
        if insights['content_patterns']['successful_keywords']:
            top_keyword = insights['content_patterns']['successful_keywords'][0][0]
            recommendations.append(f"Consider using the keyword '{top_keyword}' more often in titles")
        
        if insights['visual_patterns']['faces_correlation'] > 0.6:
            recommendations.append("Including faces in thumbnails appears to correlate with better performance")
        
        avg_engagement = performance.get('channel_metrics', {}).get('avg_engagement_rate', 0)
        if avg_engagement < 2:
            recommendations.append("Engagement rate is below average - consider more interactive content")
        
        insights['recommendations'] = recommendations
        
        return insights
    
    async def _get_cached_analysis(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get cached analysis result."""
        cache_key = f"channel_analysis:{channel_id}"
        return await self.cache.get(cache_key)
    
    async def _cache_analysis_result(self, channel_id: str, result: Dict[str, Any]):
        """Cache analysis result."""
        cache_key = f"channel_analysis:{channel_id}"
        # Cache for 24 hours
        await self.cache.set(cache_key, result, 86400)
    
    async def _store_analysis_result(self, channel_id: str, result: Dict[str, Any]):
        """Store analysis result in database."""
        try:
            await self.db.store_analysis_result(channel_id, result)

        except Exception as e:
            logger.error(f"Failed to store analysis result: {e}")
