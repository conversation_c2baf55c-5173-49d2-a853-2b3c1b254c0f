"""
YouTube Data API v3 client with quota optimization and caching.
Focuses on public data only - perfect for competitor analysis.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import hashlib

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..core.config import settings, API_QUOTA_COSTS
from ..core.memory_cache import MemoryCache

logger = logging.getLogger(__name__)


class QuotaManager:
    """Manages YouTube API quota usage and limits."""
    
    def __init__(self, cache_manager: MemoryCache):
        self.cache = cache_manager
        self.daily_limit = settings.daily_quota_limit
        self.quota_key = f"quota:usage:{datetime.now().strftime('%Y-%m-%d')}"
    
    async def get_current_usage(self) -> int:
        """Get current quota usage for today."""
        usage = await self.cache.get(self.quota_key)
        return usage or 0
    
    async def add_usage(self, cost: int) -> bool:
        """Add quota usage and check if within limits."""
        current_usage = await self.get_current_usage()
        new_usage = current_usage + cost
        
        if new_usage > self.daily_limit:
            logger.warning(f"Quota limit exceeded: {new_usage}/{self.daily_limit}")
            return False
        
        # Set with TTL until end of day
        tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        ttl_seconds = int((tomorrow - datetime.now()).total_seconds())
        
        await self.cache.set(self.quota_key, new_usage, ttl_seconds)
        logger.info(f"Quota usage: {new_usage}/{self.daily_limit}")
        return True
    
    async def can_make_request(self, endpoint: str) -> bool:
        """Check if request can be made within quota limits."""
        cost = API_QUOTA_COSTS.get(endpoint, 1)
        current_usage = await self.get_current_usage()
        return (current_usage + cost) <= self.daily_limit


class YouTubeAPIClient:
    """
    YouTube Data API v3 client with quota optimization and caching.
    Public API only - perfect for competitor channel analysis.
    """

    def __init__(self, cache_manager: MemoryCache):
        self.cache = cache_manager
        self.quota_manager = QuotaManager(cache_manager)
        self._service = None

    def _build_service(self):
        """Build YouTube Data API service using API key."""
        self._service = build('youtube', 'v3', developerKey=settings.youtube_api_key)
        return self._service
    
    async def _make_cached_request(
        self, 
        endpoint: str, 
        request_func, 
        params: Dict[str, Any],
        force_refresh: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Make API request with caching support.
        Checks cache first, then makes API call if needed.
        """
        # Check quota before making request
        if not await self.quota_manager.can_make_request(endpoint):
            raise Exception(f"Quota limit exceeded for endpoint: {endpoint}")
        
        # Check cache first (unless force refresh)
        if not force_refresh:
            cached_response = await self.cache.get_youtube_api_response(endpoint, params)
            if cached_response:
                logger.info(f"Cache hit for {endpoint}")
                return cached_response
        
        try:
            # Make API request
            logger.info(f"Making API request to {endpoint}")
            response = request_func.execute()
            
            # Update quota usage
            cost = API_QUOTA_COSTS.get(endpoint, 1)
            await self.quota_manager.add_usage(cost)
            
            # Cache the response
            await self.cache.cache_youtube_api_response(endpoint, params, response)
            
            return response
            
        except HttpError as e:
            logger.error(f"YouTube API error for {endpoint}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error for {endpoint}: {e}")
            raise
    
    async def get_channel_info(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get channel information and statistics."""
        service = self._build_service()
        
        params = {
            'part': 'snippet,statistics,contentDetails',
            'id': channel_id
        }
        
        request = service.channels().list(**params)
        
        return await self._make_cached_request(
            'channels.list',
            request,
            params
        )
    
    async def get_videos_batch(self, video_ids: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get video information in batches (up to 50 videos per request).
        This is the key optimization for quota management.
        """
        if len(video_ids) > settings.max_videos_per_batch:
            raise ValueError(f"Too many video IDs. Maximum: {settings.max_videos_per_batch}")

        service = self._build_service()
        
        params = {
            'part': 'snippet,statistics,contentDetails',
            'id': ','.join(video_ids)
        }
        
        request = service.videos().list(**params)
        
        return await self._make_cached_request(
            'videos.list',
            request,
            params
        )
    
    async def get_channel_videos(
        self,
        channel_id: str,
        max_results: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get all videos from a channel using playlist items.
        More efficient than search API (1 unit vs 100 units per call).
        """
        service = self._build_service()

        # First get the uploads playlist ID
        channel_info = await self.get_channel_info(channel_id)
        if not channel_info or 'items' not in channel_info:
            return []
        
        uploads_playlist_id = channel_info['items'][0]['contentDetails']['relatedPlaylists']['uploads']
        
        videos = []
        next_page_token = None
        
        while len(videos) < max_results:
            params = {
                'part': 'snippet,contentDetails',
                'playlistId': uploads_playlist_id,
                'maxResults': min(50, max_results - len(videos))
            }
            
            if next_page_token:
                params['pageToken'] = next_page_token
            
            request = service.playlistItems().list(**params)
            
            response = await self._make_cached_request(
                'playlistItems.list',
                request,
                params
            )
            
            if not response or 'items' not in response:
                break
            
            videos.extend(response['items'])
            next_page_token = response.get('nextPageToken')
            
            if not next_page_token:
                break
        
        return videos[:max_results]

    async def search_channels(
        self,
        query: str,
        max_results: int = 10
    ) -> Optional[Dict[str, Any]]:
        """
        Search for channels by name or keyword.
        Useful for finding competitor channels.
        """
        service = self._build_service()

        params = {
            'part': 'snippet',
            'type': 'channel',
            'q': query,
            'maxResults': max_results
        }

        request = service.search().list(**params)

        return await self._make_cached_request(
            'search.list',
            request,
            params
        )

    async def get_channel_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get channel information by username/handle."""
        service = self._build_service()

        params = {
            'part': 'snippet,statistics,contentDetails',
            'forUsername': username
        }

        request = service.channels().list(**params)

        return await self._make_cached_request(
            'channels.list',
            request,
            params
        )
