# Backend Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Database (SQLite for easy testing)
aiosqlite==0.19.0
# pymongo==4.6.0  # Commented out - using SQLite instead
# motor==3.3.2    # Commented out - using SQLite instead

# Caching (in-memory for easy testing)
# redis==5.0.1    # Commented out - using memory cache instead

# Background Tasks (commented out - not needed for simplified version)
# celery==5.3.4

# YouTube API
google-auth==2.25.2
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.2.0
google-api-python-client==2.110.0

# Data Processing (Python 3.13 compatible versions)
pandas>=2.2.0
numpy>=1.26.0
scipy>=1.12.0

# NLP (simplified - removed spacy for easier installation)
# spacy==3.7.2  # Commented out - using simpler alternatives
textblob==0.17.1
yake==0.4.8

# Computer Vision (Python 3.13 compatible versions)
opencv-python>=4.9.0
Pillow>=10.2.0
scikit-learn>=1.4.0

# Visualization
plotly>=5.18.0

# Frontend (removed from backend requirements - separate file)
# streamlit==1.28.2  # Moved to frontend/requirements.txt

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development
python-dotenv==1.0.0
black==23.11.0
flake8==6.1.0
