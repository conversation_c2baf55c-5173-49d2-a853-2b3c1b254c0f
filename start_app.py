"""
Startup script for YouTube Channel Analyser.
Starts both backend and frontend services.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def check_requirements():
    """Check if required dependencies are installed."""
    try:
        import uvicorn
        import streamlit
        print("✅ Core dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Please install requirements:")
        print("  pip install -r requirements.txt")
        print("  pip install -r frontend/requirements.txt")
        return False

def start_backend():
    """Start the FastAPI backend server."""
    print("🚀 Starting backend server...")
    
    # Change to backend directory
    backend_dir = Path(__file__).parent / "backend"
    
    # Start uvicorn server
    cmd = [
        sys.executable, "-m", "uvicorn", 
        "main:app", 
        "--host", "0.0.0.0", 
        "--port", "8000", 
        "--reload"
    ]
    
    return subprocess.Popen(cmd, cwd=backend_dir)

def start_frontend():
    """Start the Streamlit frontend."""
    print("🎨 Starting frontend dashboard...")
    
    # Change to frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    
    # Start Streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "streamlit_app.py", 
        "--server.port", "8501",
        "--server.address", "0.0.0.0"
    ]
    
    return subprocess.Popen(cmd, cwd=frontend_dir)

def main():
    """Main startup function."""
    print("🎬 YouTube Channel Analyser - Starting Application")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment file
    env_file = Path(__file__).parent / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found")
        print("Please copy .env.example to .env and configure your settings")
        print("At minimum, you need to set YOUTUBE_API_KEY")
        
        create_env = input("Create .env file from template? (y/n): ").lower().strip()
        if create_env == 'y':
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file. Please edit it with your settings.")
            print("You need to add your YouTube API key before continuing.")
            sys.exit(0)
    
    try:
        # Start backend
        backend_process = start_backend()
        time.sleep(3)  # Give backend time to start
        
        # Start frontend
        frontend_process = start_frontend()
        time.sleep(2)  # Give frontend time to start
        
        print("\n" + "=" * 50)
        print("🎉 Application started successfully!")
        print("📊 Frontend Dashboard: http://localhost:8501")
        print("🔧 Backend API: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("=" * 50)
        print("\nPress Ctrl+C to stop both services")
        
        # Wait for processes
        try:
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Shutting down services...")
            backend_process.terminate()
            frontend_process.terminate()
            
            # Wait for graceful shutdown
            backend_process.wait(timeout=5)
            frontend_process.wait(timeout=5)
            
            print("✅ Services stopped successfully")
    
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
