# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Database Configuration (SQLite - no setup required)
# Database file will be created automatically at: data/youtube_analyser.db

# Cache Configuration (In-memory - no setup required)
# Cache is handled in memory for easy testing

# Application Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True
ENVIRONMENT=development

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
STREAMLIT_SERVER_PORT=8501
BACKEND_URL=http://localhost:8000

# Caching Configuration
CACHE_TTL_HOURS=4
CACHE_TTL_COMPUTED_RESULTS=86400  # 24 hours for computed results

# YouTube API Quota Management
DAILY_QUOTA_LIMIT=10000
QUOTA_RESET_HOUR=0  # Hour when quota resets (0-23)

# Analysis Configuration
OUTLIER_METHOD=iqr  # iqr or zscore
OUTLIER_THRESHOLD=1.5  # For IQR method
ZSCORE_THRESHOLD=3.0  # For Z-score method

# Background Task Configuration
CELERY_TASK_TIMEOUT=3600  # 1 hour timeout for analysis tasks
MAX_VIDEOS_PER_BATCH=50  # YouTube API batch size limit
