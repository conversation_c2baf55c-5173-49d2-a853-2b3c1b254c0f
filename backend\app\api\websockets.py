"""
WebSocket endpoints for real-time progress updates.
"""

import json
import logging
from typing import Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
import asyncio

logger = logging.getLogger(__name__)


class ProgressManager:
    """Manages WebSocket connections for progress updates."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, channel_id: str):
        """Accept a WebSocket connection for a specific channel analysis."""
        await websocket.accept()
        self.active_connections[channel_id] = websocket
        logger.info(f"WebSocket connected for channel: {channel_id}")
    
    def disconnect(self, channel_id: str):
        """Remove a WebSocket connection."""
        if channel_id in self.active_connections:
            del self.active_connections[channel_id]
            logger.info(f"WebSocket disconnected for channel: {channel_id}")
    
    async def send_progress(self, channel_id: str, stage: str, current: int, total: int, estimated_time: float):
        """Send progress update to connected client."""
        if channel_id in self.active_connections:
            try:
                progress_data = {
                    "type": "progress",
                    "stage": stage,
                    "current": current,
                    "total": total,
                    "percentage": round((current / total) * 100, 1) if total > 0 else 0,
                    "estimated_time_remaining": round(estimated_time, 1)
                }
                
                websocket = self.active_connections[channel_id]
                await websocket.send_text(json.dumps(progress_data))
                
            except Exception as e:
                logger.error(f"Error sending progress update: {e}")
                self.disconnect(channel_id)
    
    async def send_completion(self, channel_id: str, success: bool, message: str = ""):
        """Send completion notification to connected client."""
        if channel_id in self.active_connections:
            try:
                completion_data = {
                    "type": "completion",
                    "success": success,
                    "message": message
                }
                
                websocket = self.active_connections[channel_id]
                await websocket.send_text(json.dumps(completion_data))
                
            except Exception as e:
                logger.error(f"Error sending completion update: {e}")
            finally:
                self.disconnect(channel_id)


# Global progress manager instance
progress_manager = ProgressManager()


async def websocket_endpoint(websocket: WebSocket, channel_id: str):
    """WebSocket endpoint for progress updates."""
    await progress_manager.connect(websocket, channel_id)
    
    try:
        # Keep connection alive and listen for client messages
        while True:
            try:
                # Wait for client messages (like ping/pong)
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Handle client messages if needed
                try:
                    message = json.loads(data)
                    if message.get("type") == "ping":
                        await websocket.send_text(json.dumps({"type": "pong"}))
                except json.JSONDecodeError:
                    pass
                    
            except asyncio.TimeoutError:
                # Send ping to keep connection alive
                await websocket.send_text(json.dumps({"type": "ping"}))
                
    except WebSocketDisconnect:
        progress_manager.disconnect(channel_id)
    except Exception as e:
        logger.error(f"WebSocket error for channel {channel_id}: {e}")
        progress_manager.disconnect(channel_id)
