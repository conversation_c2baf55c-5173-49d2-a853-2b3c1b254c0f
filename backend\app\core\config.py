"""
Configuration management for the YouTube Channel Analyser.
Handles environment variables and application settings.
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # YouTube API Configuration
    youtube_api_key: str
    
    # Database Configuration
    mongodb_url: str = "mongodb://localhost:27017"
    mongodb_database: str = "youtube_analyser"
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    
    # Celery Configuration
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    # Application Configuration
    secret_key: str
    debug: bool = False
    environment: str = "production"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    
    # Frontend Configuration
    streamlit_server_port: int = 8501
    backend_url: str = "http://localhost:8000"
    
    # Caching Configuration
    cache_ttl_hours: int = 4
    cache_ttl_computed_results: int = 86400  # 24 hours
    
    # YouTube API Quota Management
    daily_quota_limit: int = 10000
    quota_reset_hour: int = 0
    
    # Analysis Configuration
    outlier_method: str = "iqr"  # iqr or zscore
    outlier_threshold: float = 1.5  # For IQR method
    zscore_threshold: float = 3.0  # For Z-score method
    
    # Background Task Configuration
    celery_task_timeout: int = 3600  # 1 hour
    max_videos_per_batch: int = 50
    
    @validator("outlier_method")
    def validate_outlier_method(cls, v):
        if v not in ["iqr", "zscore"]:
            raise ValueError("outlier_method must be 'iqr' or 'zscore'")
        return v
    
    @validator("environment")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("environment must be 'development', 'staging', or 'production'")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# API Quota Costs (units per request) - Public API only
API_QUOTA_COSTS = {
    "search.list": 100,
    "videos.list": 1,
    "channels.list": 1,
    "playlistItems.list": 1,
    "commentThreads.list": 1
}

# Cache Keys
CACHE_KEYS = {
    "youtube_api": "youtube:api:{endpoint}:{params_hash}",
    "computed_results": "computed:{video_id}:{analysis_type}",
    "channel_overview": "channel:{channel_id}:overview",
    "outliers": "channel:{channel_id}:outliers:{metric}",
    "timeseries": "channel:{channel_id}:timeseries:{metric}:{start}:{end}"
}

# MongoDB Collections
COLLECTIONS = {
    "channels": "channels",
    "videos": "videos",
    "analysis_results": "analysis_results",
    "quota_usage": "quota_usage"
}
