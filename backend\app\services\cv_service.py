"""
Computer Vision service for thumbnail analysis.
Analyzes visual elements that contribute to video performance.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import io
import requests
from PIL import Image
import numpy as np
import cv2
from sklearn.cluster import KMeans
import colorsys

logger = logging.getLogger(__name__)


class ThumbnailAnalyzer:
    """
    Computer vision analysis for YouTube thumbnails.
    Extracts visual features that correlate with performance.
    """
    
    def __init__(self):
        self.face_cascade = None
        self._initialize_cv_models()
    
    def _initialize_cv_models(self):
        """Initialize OpenCV models for face detection."""
        try:
            # Load Haar cascade for face detection
            self.face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
            logger.info("Computer vision models initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize CV models: {e}")
    
    def download_image(self, url: str) -> Optional[np.ndarray]:
        """Download image from URL and convert to OpenCV format."""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # Convert to PIL Image
            pil_image = Image.open(io.BytesIO(response.content))
            
            # Convert to RGB if necessary
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert to OpenCV format (BGR)
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return cv_image
            
        except Exception as e:
            logger.error(f"Failed to download image from {url}: {e}")
            return None
    
    def detect_faces(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect faces in the image."""
        if self.face_cascade is None:
            return []
        
        try:
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            
            # Calculate face areas and positions
            face_data = []
            image_area = image.shape[0] * image.shape[1]
            
            for (x, y, w, h) in faces:
                face_area = w * h
                face_ratio = face_area / image_area
                
                # Calculate position (center of face relative to image)
                center_x = (x + w/2) / image.shape[1]
                center_y = (y + h/2) / image.shape[0]
                
                face_data.append({
                    'x': int(x),
                    'y': int(y),
                    'width': int(w),
                    'height': int(h),
                    'area_ratio': round(face_ratio, 4),
                    'center_x': round(center_x, 3),
                    'center_y': round(center_y, 3)
                })
            
            return face_data
            
        except Exception as e:
            logger.error(f"Face detection failed: {e}")
            return []
    
    def analyze_colors(self, image: np.ndarray, n_colors: int = 5) -> Dict[str, Any]:
        """Analyze dominant colors in the image."""
        try:
            # Reshape image to be a list of pixels
            pixels = image.reshape(-1, 3)
            
            # Use KMeans to find dominant colors
            kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
            kmeans.fit(pixels)
            
            # Get colors and their percentages
            colors = kmeans.cluster_centers_.astype(int)
            labels = kmeans.labels_
            
            # Calculate color percentages
            color_percentages = []
            for i in range(n_colors):
                percentage = np.sum(labels == i) / len(labels)
                
                # Convert BGR to RGB for consistency
                bgr_color = colors[i]
                rgb_color = [int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0])]
                
                # Convert to HSV for additional analysis
                hsv_color = colorsys.rgb_to_hsv(
                    rgb_color[0]/255, rgb_color[1]/255, rgb_color[2]/255
                )
                
                color_percentages.append({
                    'rgb': rgb_color,
                    'hex': f"#{rgb_color[0]:02x}{rgb_color[1]:02x}{rgb_color[2]:02x}",
                    'percentage': round(percentage * 100, 2),
                    'hue': round(hsv_color[0] * 360, 1),
                    'saturation': round(hsv_color[1] * 100, 1),
                    'brightness': round(hsv_color[2] * 100, 1)
                })
            
            # Sort by percentage
            color_percentages.sort(key=lambda x: x['percentage'], reverse=True)
            
            # Calculate overall color metrics
            avg_brightness = np.mean([c['brightness'] for c in color_percentages])
            avg_saturation = np.mean([c['saturation'] for c in color_percentages])
            
            # Determine if image is warm or cool
            warm_colors = sum(c['percentage'] for c in color_percentages 
                            if 0 <= c['hue'] <= 60 or 300 <= c['hue'] <= 360)
            cool_colors = sum(c['percentage'] for c in color_percentages 
                            if 180 <= c['hue'] <= 300)
            
            color_temperature = 'warm' if warm_colors > cool_colors else 'cool'
            
            return {
                'dominant_colors': color_percentages,
                'avg_brightness': round(avg_brightness, 1),
                'avg_saturation': round(avg_saturation, 1),
                'color_temperature': color_temperature,
                'warm_percentage': round(warm_colors, 1),
                'cool_percentage': round(cool_colors, 1)
            }
            
        except Exception as e:
            logger.error(f"Color analysis failed: {e}")
            return {}
    
    def analyze_composition(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze image composition and visual elements."""
        try:
            height, width = image.shape[:2]
            
            # Convert to grayscale for edge detection
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Edge detection
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (height * width)
            
            # Calculate brightness distribution
            brightness_hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            brightness_hist = brightness_hist.flatten() / (height * width)
            
            # Find brightness peaks
            avg_brightness = np.mean(gray)
            brightness_std = np.std(gray)
            
            # Analyze contrast
            contrast = brightness_std / avg_brightness if avg_brightness > 0 else 0
            
            # Check for text regions (high contrast areas)
            # Apply threshold to find potential text regions
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            text_ratio = np.sum(thresh == 255) / (height * width)
            
            # Analyze symmetry (simple horizontal symmetry check)
            left_half = gray[:, :width//2]
            right_half = cv2.flip(gray[:, width//2:], 1)
            
            # Resize to match if needed
            min_width = min(left_half.shape[1], right_half.shape[1])
            left_half = left_half[:, :min_width]
            right_half = right_half[:, :min_width]
            
            symmetry_score = 1 - np.mean(np.abs(left_half.astype(float) - right_half.astype(float))) / 255
            
            return {
                'edge_density': round(edge_density, 4),
                'avg_brightness': round(float(avg_brightness), 1),
                'brightness_std': round(float(brightness_std), 1),
                'contrast_ratio': round(contrast, 3),
                'text_ratio': round(text_ratio, 3),
                'symmetry_score': round(symmetry_score, 3),
                'aspect_ratio': round(width / height, 3),
                'resolution': f"{width}x{height}"
            }
            
        except Exception as e:
            logger.error(f"Composition analysis failed: {e}")
            return {}
    
    def analyze_thumbnail(self, thumbnail_url: str) -> Dict[str, Any]:
        """
        Comprehensive thumbnail analysis.
        This is the main method that combines all visual analysis features.
        """
        if not thumbnail_url:
            return {'error': 'No thumbnail URL provided'}
        
        # Download the image
        image = self.download_image(thumbnail_url)
        if image is None:
            return {'error': 'Failed to download thumbnail'}
        
        try:
            # Perform all analyses
            faces = self.detect_faces(image)
            colors = self.analyze_colors(image)
            composition = self.analyze_composition(image)
            
            # Calculate visual appeal score
            visual_score = self._calculate_visual_appeal_score(faces, colors, composition)
            
            return {
                'thumbnail_url': thumbnail_url,
                'faces': {
                    'count': len(faces),
                    'details': faces,
                    'has_faces': len(faces) > 0
                },
                'colors': colors,
                'composition': composition,
                'visual_appeal_score': visual_score,
                'analysis_success': True
            }
            
        except Exception as e:
            logger.error(f"Thumbnail analysis failed: {e}")
            return {
                'thumbnail_url': thumbnail_url,
                'error': str(e),
                'analysis_success': False
            }
    
    def _calculate_visual_appeal_score(
        self, 
        faces: List[Dict[str, Any]], 
        colors: Dict[str, Any], 
        composition: Dict[str, Any]
    ) -> float:
        """
        Calculate a visual appeal score based on various factors.
        Score ranges from 0 to 100.
        """
        score = 50  # Base score
        
        # Face factors (faces often increase engagement)
        if faces:
            score += 15  # Bonus for having faces
            
            # Bonus for optimal face size (not too small, not too large)
            for face in faces:
                face_ratio = face['area_ratio']
                if 0.05 <= face_ratio <= 0.3:  # Optimal face size
                    score += 5
                    break
        
        # Color factors
        if colors:
            avg_saturation = colors.get('avg_saturation', 0)
            avg_brightness = colors.get('avg_brightness', 0)
            
            # Optimal saturation (vibrant but not oversaturated)
            if 40 <= avg_saturation <= 80:
                score += 10
            
            # Optimal brightness (not too dark, not too bright)
            if 30 <= avg_brightness <= 70:
                score += 10
        
        # Composition factors
        if composition:
            contrast_ratio = composition.get('contrast_ratio', 0)
            edge_density = composition.get('edge_density', 0)
            
            # Good contrast helps readability
            if 0.3 <= contrast_ratio <= 0.8:
                score += 10
            
            # Moderate edge density (detailed but not cluttered)
            if 0.1 <= edge_density <= 0.3:
                score += 5
        
        return min(max(score, 0), 100)  # Clamp between 0 and 100
