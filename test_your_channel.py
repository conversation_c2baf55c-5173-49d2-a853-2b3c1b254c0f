#!/usr/bin/env python3
"""
Quick test script to analyze your YouTube channel
"""
import requests
import json
import sys

def analyze_channel(channel_id):
    """Analyze a YouTube channel and display key insights"""
    
    print(f"🎯 ANALYZING YOUR CHANNEL: {channel_id}")
    print("=" * 60)
    
    try:
        # Make API request
        response = requests.post(
            'http://localhost:8000/api/v1/analysis/channel',
            json={
                'channel_id': channel_id,
                'max_videos': 15,
                'force_refresh': True
            },
            timeout=120
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return
            
        data = response.json()
        
        # Channel Overview
        channel_info = data.get('channel_info', {})
        print(f"📺 Channel: {channel_info.get('title', 'Unknown')}")
        print(f"👥 Subscribers: {channel_info.get('subscriber_count', 0):,}")
        print(f"🎬 Total Videos: {channel_info.get('video_count', 0):,}")
        print(f"👀 Total Views: {channel_info.get('view_count', 0):,}")
        print()
        
        # Performance Metrics
        metrics = data.get('performance_metrics', {})
        print("📊 PERFORMANCE METRICS:")
        print(f"   Average Views: {metrics.get('avg_views', 0):,.0f}")
        print(f"   Average Engagement: {metrics.get('avg_engagement_rate', 0):.2%}")
        print(f"   Upload Frequency: {metrics.get('upload_frequency', 0):.1f} videos/week")
        print()
        
        # Top Performing Videos (Your Outliers!)
        videos = data.get('video_analysis', [])
        if videos:
            print("🚀 YOUR TOP PERFORMING VIDEOS (OUTLIERS):")
            sorted_videos = sorted(videos, key=lambda x: x.get('view_count', 0), reverse=True)[:5]
            for i, video in enumerate(sorted_videos, 1):
                title = video.get('title', 'Unknown')
                if len(title) > 50:
                    title = title[:50] + "..."
                views = video.get('view_count', 0)
                engagement = video.get('engagement_rate', 0)
                print(f"   {i}. {title}")
                print(f"      👀 Views: {views:,} | 💬 Engagement: {engagement:.2%}")
            print()
        
        # Content Insights
        insights = data.get('insights', {})
        content_patterns = insights.get('content_patterns', {})
        if content_patterns:
            print("💡 CONTENT INSIGHTS:")
            common_keywords = content_patterns.get('common_keywords', [])[:5]
            if common_keywords:
                print(f"   🔑 Top Keywords: {', '.join(common_keywords)}")
            
            avg_title_length = content_patterns.get('avg_title_length', 0)
            if avg_title_length:
                print(f"   📝 Average Title Length: {avg_title_length:.0f} characters")
            print()
        
        # Recommendations
        recommendations = insights.get('recommendations', [])
        if recommendations:
            print("🎯 ACTIONABLE RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"   {i}. {rec}")
            print()
        
        print("✅ Analysis Complete!")
        print("💡 Next Steps:")
        print("   1. Check the Streamlit interface at http://localhost:8501 for detailed visualizations")
        print("   2. Analyze 3-5 competitor channels for comparison")
        print("   3. Look for patterns in your top-performing videos")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to backend. Make sure it's running on port 8000")
    except Exception as e:
        print(f"❌ Error analyzing channel: {e}")

if __name__ == "__main__":
    # Your channel ID
    your_channel_id = "UCTDIxt3MAZY78JZsq-aw_iA"
    analyze_channel(your_channel_id)
