"""
API endpoints for channel analysis functionality.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from ...services.analysis_service import ChannelAnalysisService
from ...services.youtube_client import YouTubeAPIClient
from ...core.sqlite_database import get_sqlite_database
from ...core.memory_cache import get_memory_cache

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["analysis"])


class AnalysisRequest(BaseModel):
    channel_id: str = Field(..., description="YouTube channel ID")
    max_videos: int = Field(50, ge=1, le=200, description="Maximum number of videos to analyze")
    force_refresh: bool = Field(False, description="Force refresh cached data")


class ChannelSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, description="Search query for channels")
    max_results: int = Field(10, ge=1, le=50, description="Maximum number of results")


async def get_analysis_service() -> ChannelAnalysisService:
    """Dependency to get analysis service."""
    database = await get_sqlite_database()
    cache_manager = await get_memory_cache()
    return ChannelAnalysisService(database, cache_manager)


async def get_youtube_client() -> YouTubeAPIClient:
    """Dependency to get YouTube client."""
    cache_manager = await get_cache_manager()
    return YouTubeAPIClient(cache_manager)


@router.post("/channel", response_model=Dict[str, Any])
async def analyze_channel(
    request: AnalysisRequest,
    analysis_service: ChannelAnalysisService = Depends(get_analysis_service)
):
    """
    Analyze a YouTube channel comprehensively.
    
    This endpoint performs a complete analysis including:
    - Channel and video metrics
    - Content analysis (NLP)
    - Thumbnail analysis (Computer Vision)
    - Performance insights and recommendations
    """
    try:
        logger.info(f"Starting channel analysis for: {request.channel_id}")
        
        result = await analysis_service.analyze_channel(
            channel_id=request.channel_id,
            max_videos=request.max_videos,
            force_refresh=request.force_refresh
        )
        
        if 'error' in result:
            raise HTTPException(status_code=404, detail=result['error'])
        
        return {
            "success": True,
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Channel analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/channel/{channel_id}", response_model=Dict[str, Any])
async def get_channel_analysis(
    channel_id: str,
    force_refresh: bool = Query(False, description="Force refresh cached data"),
    analysis_service: ChannelAnalysisService = Depends(get_analysis_service)
):
    """
    Get existing analysis for a channel or perform new analysis.
    """
    try:
        result = await analysis_service.analyze_channel(
            channel_id=channel_id,
            max_videos=50,
            force_refresh=force_refresh
        )
        
        if 'error' in result:
            raise HTTPException(status_code=404, detail=result['error'])
        
        return {
            "success": True,
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get channel analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get analysis: {str(e)}")


@router.post("/search-channels", response_model=Dict[str, Any])
async def search_channels(
    request: ChannelSearchRequest,
    youtube_client: YouTubeAPIClient = Depends(get_youtube_client)
):
    """
    Search for YouTube channels by name or keyword.
    Useful for finding competitor channels to analyze.
    """
    try:
        result = await youtube_client.search_channels(
            query=request.query,
            max_results=request.max_results
        )
        
        if not result or 'items' not in result:
            return {
                "success": True,
                "data": {
                    "channels": [],
                    "total_results": 0
                }
            }
        
        # Format the results
        channels = []
        for item in result['items']:
            snippet = item.get('snippet', {})
            channels.append({
                'channel_id': snippet.get('channelId', ''),
                'title': snippet.get('title', ''),
                'description': snippet.get('description', ''),
                'thumbnail_url': snippet.get('thumbnails', {}).get('high', {}).get('url', ''),
                'published_at': snippet.get('publishedAt', '')
            })
        
        return {
            "success": True,
            "data": {
                "channels": channels,
                "total_results": len(channels),
                "query": request.query
            }
        }
        
    except Exception as e:
        logger.error(f"Channel search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/channel/{channel_id}/basic-info", response_model=Dict[str, Any])
async def get_channel_basic_info(
    channel_id: str,
    youtube_client: YouTubeAPIClient = Depends(get_youtube_client)
):
    """
    Get basic channel information without full analysis.
    Useful for quick channel lookup and validation.
    """
    try:
        result = await youtube_client.get_channel_info(channel_id)
        
        if not result or 'items' not in result or not result['items']:
            raise HTTPException(status_code=404, detail="Channel not found")
        
        channel_data = result['items'][0]
        snippet = channel_data.get('snippet', {})
        statistics = channel_data.get('statistics', {})
        
        return {
            "success": True,
            "data": {
                'channel_id': channel_data.get('id'),
                'title': snippet.get('title', ''),
                'description': snippet.get('description', ''),
                'subscriber_count': int(statistics.get('subscriberCount', 0)),
                'video_count': int(statistics.get('videoCount', 0)),
                'view_count': int(statistics.get('viewCount', 0)),
                'created_at': snippet.get('publishedAt', ''),
                'thumbnail_url': snippet.get('thumbnails', {}).get('high', {}).get('url', ''),
                'country': snippet.get('country', ''),
                'custom_url': snippet.get('customUrl', '')
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get channel info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get channel info: {str(e)}")


@router.get("/channel/{channel_id}/videos", response_model=Dict[str, Any])
async def get_channel_videos(
    channel_id: str,
    max_results: int = Query(50, ge=1, le=200, description="Maximum number of videos"),
    youtube_client: YouTubeAPIClient = Depends(get_youtube_client)
):
    """
    Get list of videos from a channel without full analysis.
    """
    try:
        videos_list = await youtube_client.get_channel_videos(channel_id, max_results)
        
        if not videos_list:
            return {
                "success": True,
                "data": {
                    "videos": [],
                    "total_count": 0
                }
            }
        
        # Extract video IDs for batch processing
        video_ids = [item['snippet']['resourceId']['videoId'] for item in videos_list]
        
        # Get detailed video information in batches
        all_videos_data = []
        batch_size = 50
        
        for i in range(0, len(video_ids), batch_size):
            batch_ids = video_ids[i:i + batch_size]
            batch_data = await youtube_client.get_videos_batch(batch_ids)
            
            if batch_data and 'items' in batch_data:
                all_videos_data.extend(batch_data['items'])
        
        # Format video data
        formatted_videos = []
        for video in all_videos_data:
            snippet = video.get('snippet', {})
            statistics = video.get('statistics', {})
            
            formatted_videos.append({
                'video_id': video.get('id'),
                'title': snippet.get('title', ''),
                'description': snippet.get('description', ''),
                'published_at': snippet.get('publishedAt', ''),
                'thumbnail_url': snippet.get('thumbnails', {}).get('high', {}).get('url', ''),
                'view_count': int(statistics.get('viewCount', 0)),
                'like_count': int(statistics.get('likeCount', 0)),
                'comment_count': int(statistics.get('commentCount', 0)),
                'duration': video.get('contentDetails', {}).get('duration', '')
            })
        
        return {
            "success": True,
            "data": {
                "videos": formatted_videos,
                "total_count": len(formatted_videos),
                "channel_id": channel_id
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get channel videos: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get videos: {str(e)}")


@router.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint for the analysis API."""
    return {"status": "healthy", "service": "analysis_api"}
