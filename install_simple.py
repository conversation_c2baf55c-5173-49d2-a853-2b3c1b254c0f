#!/usr/bin/env python3
"""
Simple installation script for Python 3.13 compatibility.
Installs only essential dependencies to avoid compilation issues.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_dependencies():
    """Install dependencies with fallback options."""
    print("🚀 Installing YouTube Channel Analyser dependencies...")
    
    # Essential backend dependencies
    essential_deps = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0", 
        "pydantic>=2.5.0",
        "python-multipart>=0.0.6",
        "aiosqlite>=0.19.0",
        "google-auth>=2.25.0",
        "google-auth-oauthlib>=1.1.0", 
        "google-auth-httplib2>=0.2.0",
        "google-api-python-client>=2.110.0",
        "textblob>=0.17.0",
        "yake>=0.4.8",
        "python-dotenv>=1.0.0",
        "requests>=2.31.0"
    ]
    
    # Frontend dependencies
    frontend_deps = [
        "streamlit>=1.28.0",
        "plotly>=5.15.0"
    ]
    
    # Optional dependencies (may fail on Python 3.13)
    optional_deps = [
        "pandas>=2.2.0",
        "numpy>=1.26.0", 
        "opencv-python>=4.9.0",
        "scikit-learn>=1.4.0",
        "Pillow>=10.2.0"
    ]
    
    print("📦 Installing essential dependencies...")
    for dep in essential_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    print("📦 Installing frontend dependencies...")
    for dep in frontend_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    print("📦 Installing optional dependencies (may skip some on Python 3.13)...")
    for dep in optional_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Skipped {dep} (compilation issues on Python 3.13)")
    
    print("\n✅ Installation completed!")
    print("\n📋 Next steps:")
    print("1. Copy .env.example to .env and add your YouTube API key")
    print("2. Run: python start_simple.py")
    print("3. Open: http://localhost:8501")

def check_python_version():
    """Check Python version and warn about compatibility."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 13:
        print("⚠️  Python 3.13+ detected - using simplified dependencies to avoid compilation issues")
        return True
    elif version.major == 3 and version.minor >= 8:
        print("✅ Python version compatible")
        return True
    else:
        print("❌ Python 3.8+ required")
        return False

def main():
    """Main installation function."""
    print("🎬 YouTube Channel Analyser - Simple Installation")
    print("=" * 50)
    
    if not check_python_version():
        sys.exit(1)
    
    # Check if .env file exists
    if not Path(".env").exists():
        if Path(".env.example").exists():
            print("📝 Creating .env file from .env.example...")
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ .env file created - please add your YouTube API key")
        else:
            print("⚠️  No .env.example found - you'll need to create .env manually")
    
    install_dependencies()

if __name__ == "__main__":
    main()
