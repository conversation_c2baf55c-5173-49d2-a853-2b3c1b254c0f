#!/usr/bin/env python3
"""
Simple startup script for YouTube Channel Analyser.
Uses simplified analysis service to avoid dependency issues.
"""

import subprocess
import sys
import time
import os
from pathlib import Path
import threading

def check_dependencies():
    """Check if essential dependencies are installed."""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'streamlit',
        'aiosqlite',
        'google.auth'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        print("Run: python install_simple.py")
        return False
    
    return True

def check_env_file():
    """Check if .env file exists and has YouTube API key."""
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env file not found")
        print("Copy .env.example to .env and add your YouTube API key")
        return False
    
    # Check for API key
    try:
        with open(env_path, 'r') as f:
            content = f.read()
            if 'YOUTUBE_API_KEY=your_youtube_api_key_here' in content or 'YOUTUBE_API_KEY=' not in content:
                print("⚠️  YouTube API key not configured in .env file")
                print("Please add your YouTube API key to the .env file")
                return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False
    
    return True

def start_backend():
    """Start the FastAPI backend."""
    print("🚀 Starting backend server...")
    try:
        # Change to backend directory
        backend_dir = Path("backend")
        if backend_dir.exists():
            os.chdir(backend_dir)
        
        # Start uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
    except Exception as e:
        print(f"❌ Backend server error: {e}")

def start_frontend():
    """Start the Streamlit frontend."""
    print("🎨 Starting frontend dashboard...")
    try:
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Change to frontend directory
        frontend_dir = Path("frontend")
        if frontend_dir.exists():
            os.chdir(frontend_dir)
        
        # Start streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Frontend dashboard stopped")
    except Exception as e:
        print(f"❌ Frontend dashboard error: {e}")

def main():
    """Main startup function."""
    print("🎬 YouTube Channel Analyser - Simple Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check environment
    if not check_env_file():
        sys.exit(1)
    
    print("✅ All checks passed!")
    print("\n🚀 Starting application...")
    print("📊 Dashboard will be available at: http://localhost:8501")
    print("🔧 API will be available at: http://localhost:8000")
    print("📚 API docs will be available at: http://localhost:8000/docs")
    print("\nPress Ctrl+C to stop both services")
    
    # Start backend in a separate thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Start frontend in main thread
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\n🛑 Application stopped")

if __name__ == "__main__":
    main()
